{"name": "md-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-amplify/auth": "^6.13.3", "@aws-amplify/core": "^6.12.3", "@aws-amplify/ui-react": "^6.11.2", "@aws-amplify/ui-react-notifications": "^2.2.9", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.17.1", "@mui/x-data-grid": "^8.7.0", "@mui/x-date-pickers": "^8.7.0", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.81.5", "@types/qrcode.react": "^1.0.5", "aws-amplify": "^6.15.3", "date-fns": "^4.1.0", "formik": "^2.4.6", "next": "15.3.5", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "yup": "^1.6.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/redux-logger": "^3.0.13", "eslint": "^9", "eslint-config-next": "15.3.5", "typescript": "^5"}}