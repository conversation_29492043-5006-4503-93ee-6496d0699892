'use client';

import { useState } from 'react';
import { Box, Typography, Paper, Alert } from '@mui/material';
import { AdminUserForm } from '@/components/admin/AdminUserForm';

export default function TestAdminFormPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (data: any) => {
    console.log('Form submitted with data:', data);
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess('Admin user created successfully!');
      console.log('Success! User data:', data);
    } catch (err: any) {
      setError(err.message || 'Failed to create admin user');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    console.log('Form cancelled');
    setError(null);
    setSuccess(null);
  };

  return (
    <Box sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🧪 Test Admin User Form
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
        This page is for testing the admin user creation form and API endpoint.
        Fill out the form and click "Create User" to test the functionality.
      </Typography>

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 3 }}>
        <AdminUserForm
          currentUserRole="super_admin"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
          error={error}
        />
      </Paper>

      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          📝 Testing Instructions
        </Typography>
        
        <Typography variant="body2" component="div">
          <ol>
            <li>Fill out all required fields in the form above</li>
            <li>Click "Create User" button</li>
            <li>Check the browser console for API call details</li>
            <li>Verify that the API endpoint is being called correctly</li>
            <li>Check the Network tab in DevTools to see the actual HTTP request</li>
          </ol>
        </Typography>
        
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>API Endpoint:</strong> POST /api/admin/register<br/>
            <strong>Expected Payload:</strong> {`{ new_admin: { username, firstName, lastName, email, password, role }, token: "access_token" }`}
          </Typography>
        </Alert>
      </Paper>
    </Box>
  );
}
