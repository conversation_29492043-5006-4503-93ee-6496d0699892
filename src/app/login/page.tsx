"use client";

import { <PERSON><PERSON>r<PERSON>oundary, <PERSON>ginForm, LoginLayout, MFAForm, RegistrationProcessing, TOTPSetupForm } from "@/components/auth";
import "@/config/cognito";
import { useAuth } from "@/hooks/useAuth";
import { useLoginState } from "@/hooks/useLoginState";
import { RootState } from "@/store";
import { AUTH_STEPS, AuthFlowManager } from "@/utils/authFlow";
import { Alert } from "@mui/material";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";

export default function LoginPage() {
  const tempRegistration = useSelector((state: RootState) => state.auth.tempRegistration);
  const { isLoading, error, handleSignIn, handleMFAConfirmation, handleTOTPSetup, setupTOTPAfterLogin, clearError } = useAuth();

  const {
    loginData,
    setLoginData,
    mfaData,
    setMfaData,
    mfaRequired,
    mfaType,
    totpSetup,
    setTotpSetup,
    resetMfaData,
    handleMfaStep,
  } = useLoginState();

  useEffect(() => {
    const handleTempRegistration = async () => {
      if (tempRegistration?.username && tempRegistration?.password) {
        // Set the login data from temp registration for potential TOTP setup
        if (tempRegistration.email) {
          setLoginData({
            username: tempRegistration.username,
            password: tempRegistration.password
          });
        }

        try {
          const result = await handleSignIn({
            username: tempRegistration.username,
            password: tempRegistration.password,
          });

          AuthFlowManager.logAuthResult(result, "Auto sign-in");
          await handleAuthResult(result, tempRegistration.username);
        } catch (error) {
          console.error("Auto sign-in failed:", error);
        }
      }
    };

    handleTempRegistration();
  }, [tempRegistration, handleSignIn, setLoginData]);

  const handleAuthResult = async (result: any, username: string) => {
    if (AuthFlowManager.shouldSetupTOTP(result, username)) {
      try {
        const setupUri = await setupTOTPAfterLogin(username);
        setTotpSetup({ required: true, sharedSecret: setupUri });
      } catch (error) {
        console.error("TOTP setup failed:", error);
      }
      return;
    }

    const mfaStep = AuthFlowManager.getMFAStepType(result);
    if (mfaStep) {
      if (mfaStep === AUTH_STEPS.SETUP_TOTP) {
        const sharedSecret = AuthFlowManager.getSharedSecret(result);
        if (sharedSecret) {
          setTotpSetup({ required: true, sharedSecret });
        }
      } else {
        handleMfaStep(mfaStep, result);
      }
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();
    resetMfaData();

    try {
      const result = await handleSignIn(loginData);
      AuthFlowManager.logAuthResult(result, "Login");
      await handleAuthResult(result, loginData.username);
    } catch (err: any) {
      console.error("Login error:", err);
    }
  };

  const handleConfirmMFA = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    try {
      await handleMFAConfirmation(mfaData);
    } catch (err: any) {
      console.error("MFA confirmation error:", err);
    }
  };

  const handleTotpSetupSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    try {
      await handleTOTPSetup(mfaData.code);
    } catch (err: any) {
      console.error("TOTP setup error:", err);
    }
  };

  // Get page title and subtitle based on current state
  const getPageTitle = () => "Welcome to CO";

  const getPageSubtitle = () => {
    if (totpSetup.required) {
      return "Set up Two-Factor Authentication";
    } else if (mfaRequired) {
      return mfaType === "TOTP"
        ? "Enter your Authenticator App code"
        : "Enter the SMS code sent to your phone";
    } else if (tempRegistration?.email) {
      return "Completing your registration...";
    } else {
      return "Member Dashboard Login";
    }
  };

  // Don't show login form if temp registration is in progress
  const shouldShowLoginForm = !tempRegistration?.email && !totpSetup.required && !mfaRequired;

  return (
    <ErrorBoundary>
      <LoginLayout title={getPageTitle()} subtitle={getPageSubtitle()}>
        {error && (
          <Alert severity="error" sx={{ mb: 2, width: "100%" }}>
            {error}
          </Alert>
        )}

        {totpSetup.required && totpSetup.sharedSecret ? (
          <TOTPSetupForm
            sharedSecret={totpSetup.sharedSecret}
            username={tempRegistration?.username || loginData.username}
            mfaData={mfaData}
            setMfaData={setMfaData}
            onSubmit={handleTotpSetupSubmit}
            isLoading={isLoading}
          />
        ) : mfaRequired ? (
          <MFAForm
            mfaData={mfaData}
            setMfaData={setMfaData}
            onSubmit={handleConfirmMFA}
            isLoading={isLoading}
          />
        ) : shouldShowLoginForm ? (
          <LoginForm
            loginData={loginData}
            setLoginData={setLoginData}
            onSubmit={handleLogin}
            isLoading={isLoading}
          />
        ) : (
          <RegistrationProcessing message="Completing your registration. Please wait..." />
        )}
      </LoginLayout>
    </ErrorBoundary>
  );
}
