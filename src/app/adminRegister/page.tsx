"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Ty<PERSON>graphy,
  Checkbox,
  FormControlLabel,
  Alert,
  Container,
  Paper,
  Stack,
} from "@mui/material";
import { signUp, confirmSignUp } from "aws-amplify/auth";
import React, { useState } from "react";
import Image from "next/image";
import Link from "@/components/Link";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { setTempRegistrationCredentials } from "@/store/auth/redux";

const AdminRegister = () => {
  const [step, setStep] = useState<"signup" | "confirm">("signup");
  const [code, setCode] = useState("");
  const [username, setUsername] = useState("user123");
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("");
  const [enableTOTP, setEnableTOTP] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();

  const router = useRouter();

  const handleRegister = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { isSignUpComplete, userId, nextStep } = await signUp({
        username,
        password,
        options: {
          userAttributes: {
            email,
          },
        },
      });

      console.log({ userId, isSignUpComplete, nextStep });
      dispatch(setTempRegistrationCredentials({
        username,
        password,
        email,
      }));

      if (nextStep?.signUpStep === "CONFIRM_SIGN_UP") {
        setStep("confirm");
      } else {
        alert("Signup complete!");
      }
    } catch (error: any) {
      console.error("Signup Error:", error);
      setError(error.message || "Signup failed");
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirm = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await confirmSignUp({ username, confirmationCode: code });
      console.log("Confirmation Result:", result);

      router.push("/login");

    } catch (error: any) {
      console.error("Confirmation Error:", error);
      setError(error.message || "Confirmation failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      sx={{
        height: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background: "linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)",
        overflow: "hidden",
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container maxWidth="sm" disableGutters>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 3, sm: 5 },
            borderRadius: 4,
            boxShadow: "0 8px 32px 0 rgba(30,58,138,0.2)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "90vw", sm: 500 },
            maxWidth: 520,
            mx: "auto",
            maxHeight: "90vh",
            overflowY: "auto",
          }}
        >
          <Box sx={{ mb: 3, display: "flex", justifyContent: "center" }}>
            <Image
              src="/CO-gold.svg"
              alt="US Chamber CO Logo"
              width={120}
              height={56}
              style={{ objectFit: "contain" }}
            />
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2, width: "100%" }}>
              {error}
            </Alert>
          )}

          {step === "signup" ? (
            <>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                align="center"
                sx={{ fontWeight: 700, color: "#1e3a8a" }}
              >
                Admin Register
              </Typography>

              <Typography
                variant="subtitle1"
                align="center"
                sx={{ mb: 2, color: "#555" }}
              >
                Create Your Admin Account
              </Typography>

              <Box sx={{ mt: 2, width: "100%" }}>
                <Stack spacing={2}>
                  <TextField
                    fullWidth
                    label="Username"
                    placeholder="Enter your username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    sx={{ background: "#fafbfc", borderRadius: 1 }}
                    autoFocus
                  />
                  <TextField
                    fullWidth
                    label="Email"
                    placeholder="Enter your email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    sx={{ background: "#fafbfc", borderRadius: 1 }}
                  />
                  <TextField
                    fullWidth
                    label="Password"
                    placeholder="Enter your password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    sx={{ background: "#fafbfc", borderRadius: 1 }}
                  />

                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={enableTOTP}
                        onChange={(e) => setEnableTOTP(e.target.checked)}
                        sx={{ color: "#1e3a8a" }}
                      />
                    }
                    label="Enable Two-Factor Authentication (TOTP)"
                    sx={{ mt: 2, alignSelf: "flex-start", color: "#555" }}
                  />

                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleRegister}
                    disabled={isLoading}
                    sx={{
                      mt: 3,
                      mb: 2,
                      fontWeight: 700,
                      background:
                        "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                      color: "#fff",
                      boxShadow: "0 2px 8px 0 rgba(30,58,138,0.10)",
                      "&:hover": {
                        background:
                          "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                        opacity: 0.95,
                      },
                      "&:disabled": {
                        opacity: 0.6,
                      },
                    }}
                  >
                    {isLoading ? "Registering..." : "Register"}
                  </Button>
                </Stack>
              </Box>
            </>
          ) : (
            <>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                align="center"
                sx={{ fontWeight: 700, color: "#1e3a8a" }}
              >
                Confirm Sign Up
              </Typography>

              <Typography
                variant="subtitle1"
                align="center"
                sx={{ mb: 2, color: "#555" }}
              >
                Check your email for the confirmation code
              </Typography>

              <Box sx={{ mt: 2, width: "100%" }}>
                <Stack spacing={2}>
                  <TextField
                    fullWidth
                    label="Confirmation Code"
                    placeholder="Enter the confirmation code"
                    value={code}
                    onChange={(e) => setCode(e.target.value)}
                    sx={{ background: "#fafbfc", borderRadius: 1 }}
                    autoFocus
                  />
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleConfirm}
                    disabled={isLoading}
                    sx={{
                      mt: 3,
                      mb: 2,
                      fontWeight: 700,
                      background:
                        "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                      color: "#fff",
                      boxShadow: "0 2px 8px 0 rgba(30,58,138,0.10)",
                      "&:hover": {
                        background:
                          "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
                        opacity: 0.95,
                      },
                      "&:disabled": {
                        opacity: 0.6,
                      },
                    }}
                  >
                    {isLoading ? "Confirming..." : "Confirm Sign Up"}
                  </Button>
                </Stack>
              </Box>

              <Typography
                align="center"
                sx={{
                  color: "#555",
                  fontSize: "14px",
                  mt: 2,
                }}
              >
                <Link
                  href="/login"
                  sx={{
                    color: "#1e3a8a",
                    textDecoration: "none",
                    "&:hover": {
                      textDecoration: "underline",
                    },
                  }}
                >
                  Back to Login
                </Link>
              </Typography>
            </>
          )}
        </Paper>
      </Container>
    </Box>
  );
};

export default AdminRegister;
