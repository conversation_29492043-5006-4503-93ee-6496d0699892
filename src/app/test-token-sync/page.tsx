'use client';

import { useState, useEffect } from 'react';
import { Box, Button, Typography, Paper, Alert, Chip } from '@mui/material';
import { getCognitoTokens, getCognitoTokensFromCookies, getCognitoCurrentUser, syncCognitoTokensToCookies } from '@/utils/auth';
import { useCognitoTokenSync } from '@/hooks/useCognitoTokenSync';

export default function TestTokenSyncPage() {
  const [localStorageTokens, setLocalStorageTokens] = useState<any>(null);
  const [cookieTokens, setCookieTokens] = useState<any>(null);
  const [currentUser, setCurrentUser] = useState<string | null>(null);
  const [lastSync, setLastSync] = useState<string | null>(null);
  
  const { syncTokens, hasTokensInLocalStorage } = useCognitoTokenSync({
    autoSync: false // Disable auto sync for this test page
  });

  const refreshData = () => {
    const user = getCognitoCurrentUser();
    const localTokens = getCognitoTokens(user || undefined);
    const cookieTokensData = getCognitoTokensFromCookies();
    
    setCurrentUser(user);
    setLocalStorageTokens(localTokens);
    setCookieTokens(cookieTokensData);
  };

  const handleManualSync = () => {
    const success = syncTokens();
    if (success) {
      setLastSync(new Date().toLocaleTimeString());
      setTimeout(refreshData, 500); // Refresh after sync
    }
  };

  useEffect(() => {
    refreshData();
    
    // Refresh data every 5 seconds
    const interval = setInterval(refreshData, 5000);
    return () => clearInterval(interval);
  }, []);

  const formatToken = (token: string | null) => {
    if (!token) return 'Not found';
    return `${token.substring(0, 20)}...${token.substring(token.length - 10)}`;
  };

  const getTokenStatus = (localToken: string | null, cookieToken: string | null) => {
    if (!localToken && !cookieToken) return { status: 'none', color: 'default' as const };
    if (localToken && cookieToken && localToken === cookieToken) return { status: 'synced', color: 'success' as const };
    if (localToken && !cookieToken) return { status: 'local-only', color: 'warning' as const };
    if (!localToken && cookieToken) return { status: 'cookie-only', color: 'info' as const };
    return { status: 'mismatch', color: 'error' as const };
  };

  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🔐 Cognito Token Sync Test Page
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3, color: 'text.secondary' }}>
        This page shows the current state of Cognito tokens in localStorage and cookies.
        Use this to verify that token synchronization is working correctly.
      </Typography>

      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button variant="contained" onClick={refreshData}>
          🔄 Refresh Data
        </Button>
        <Button 
          variant="outlined" 
          onClick={handleManualSync}
          disabled={!hasTokensInLocalStorage()}
        >
          🔗 Manual Sync
        </Button>
      </Box>

      {lastSync && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Last manual sync: {lastSync}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          📊 Current Status
        </Typography>
        
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2">Current User:</Typography>
          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
            {currentUser || 'No user found'}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2">Has Tokens in localStorage:</Typography>
          <Chip 
            label={hasTokensInLocalStorage() ? 'Yes' : 'No'} 
            color={hasTokensInLocalStorage() ? 'success' : 'default'}
            size="small"
          />
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🔑 Token Comparison
        </Typography>
        
        {['accessToken', 'idToken', 'refreshToken'].map((tokenType) => {
          const localToken = localStorageTokens?.[tokenType];
          const cookieToken = cookieTokens?.[tokenType];
          const status = getTokenStatus(localToken, cookieToken);
          
          return (
            <Box key={tokenType} sx={{ mb: 3, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                  {tokenType}
                </Typography>
                <Chip 
                  label={status.status} 
                  color={status.color}
                  size="small"
                />
              </Box>
              
              <Box sx={{ mb: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  localStorage:
                </Typography>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                  {formatToken(localToken)}
                </Typography>
              </Box>
              
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Cookie:
                </Typography>
                <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem' }}>
                  {formatToken(cookieToken)}
                </Typography>
              </Box>
            </Box>
          );
        })}
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          📝 Instructions
        </Typography>
        
        <Typography variant="body2" component="div">
          <ol>
            <li>Log in to your application</li>
            <li>Come back to this page to see token status</li>
            <li>Verify that tokens appear in both localStorage and cookies</li>
            <li>Check that all tokens show "synced" status</li>
            <li>Test MFA/TOTP flows and verify tokens remain synced</li>
          </ol>
        </Typography>
        
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>Note:</strong> Tokens are automatically synced every 30 seconds and when authentication events occur.
            The sync status should show "synced" when everything is working correctly.
          </Typography>
        </Alert>
      </Paper>
    </Box>
  );
}
