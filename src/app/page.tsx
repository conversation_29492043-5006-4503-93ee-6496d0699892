'use client';

import { useSearchParams } from '@/hooks/useSearchParams';
import { loginUser, socialLogin, validateCode } from '@/services/loginAPI';
import { useAppDispatch } from '@/store/hooks';
import {
  loginFormRequest,
  socialLoginRequest as reduxSocialLoginRequest
} from '@/store/auth/redux';
import { setAuthToken } from '@/utils/auth';
import { showToast } from '@/utils/toast';
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useFormik } from 'formik';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import * as Yup from 'yup';

export default function LoginPage() {
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  const router = useRouter();
  const dispatch = useAppDispatch();
  const loginToastShown = useRef(false);
  const codeValidationToastShown = useRef(false);
  const [isProcessingCode, setIsProcessingCode] = useState(false);
  const [codeProcessed, setCodeProcessed] = useState(false);

  const initialValues = {
    loginemail: '',
    password: ''
  };

  const validationSchema = Yup.object({
    loginemail: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string().min(8, 'Password must be at least 8 characters').required('Password is required'),
  });

  // Login mutation using TanStack Query
  const loginRequest = useMutation({
    mutationFn: loginUser,
    onSuccess: (data) => {
      // Also dispatch to Redux
      dispatch(loginFormRequest(initialValues));
    },
    onError: (error: any) => {
      console.error('Login error:', error);
    },
  });

  // Social login mutation using TanStack Query
  const socialLoginRequest = useMutation({
    mutationFn: socialLogin,
    onSuccess: (data) => {
      // Also dispatch to Redux for the current provider
      dispatch(reduxSocialLoginRequest({ provider: 'google' }));
    },
    onError: (error: any) => {
      console.error('Social login error:', error);
    },
  });

  // Code validation query using TanStack Query
  const codeValidateRequest = useQuery({
    queryKey: ['validateCode', code],
    queryFn: () => validateCode(code!),
    enabled: false, // We'll trigger this manually
    retry: false,
  });

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      const loadingToast = showToast.loading('Logging in...');
      try {
        const data = await loginRequest.mutateAsync(values);
        showToast.dismiss(loadingToast);

        if (data && data.success) {
          setAuthToken(data.token!);
          if (!loginToastShown.current) {
            showToast.success('Login successful! Redirecting...');
            loginToastShown.current = true;
          }
          router.push('/dashboard');
        } else {
          showToast.error('Login failed. Please check your credentials.');
        }
      } catch (error) {
        showToast.dismiss(loadingToast);
        showToast.error('An error occurred during login. Please try again.');
      }
    },
  });

  const handleSocialLogin = async (provider: 'google' | 'apple' | 'linkedin') => {
    const loadingToast = showToast.loading(`Redirecting to ${provider}...`);
    try {
      console.log('first');
      const response = await socialLoginRequest.mutateAsync({ provider }) || { status_code: 500, success: false, url: '' };
      const { success } = response;
      const url = 'url' in response ? response.url : '';

      showToast.dismiss(loadingToast);

      if (success) {
        window.location.href = url;
      } else {
        showToast.error(`${provider.charAt(0).toUpperCase() + provider.slice(1)} login failed. Please try again.`);
      }
    } catch (error) {
      showToast.dismiss(loadingToast);
      showToast.error(`An error occurred during ${provider} login. Please try again.`);
    }
  };

  const { dirty, isValid, handleSubmit, handleBlur, handleChange, values, errors, touched } = formik;

  // Handle code validation effect - improved to prevent loops
  useEffect(() => {
    if (code && !codeProcessed && !isProcessingCode) {
      setIsProcessingCode(true);
      setCodeProcessed(true);

      const loadingToast = showToast.loading('Validating login...');

      // Clear the code from URL to prevent re-processing
      const url = new URL(window.location.href);
      url.searchParams.delete('code');
      window.history.replaceState({}, '', url.toString());

      codeValidateRequest.refetch().then((response) => {
        showToast.dismiss(loadingToast);
        if (response && response.data && response.data.status_code === 200) {
          setAuthToken(response.data.token!);
          if (!codeValidationToastShown.current) {
            showToast.success('Login successful! Redirecting...');
            codeValidationToastShown.current = true;
          }
          // Add a small delay to ensure token is set before redirect
          setTimeout(() => {
            router.push('/dashboard');
          }, 500);
        } else {
          showToast.error('Code validation failed. Please try logging in again.');
          setIsProcessingCode(false);
        }
      }).catch((error) => {
        showToast.dismiss(loadingToast);
        showToast.error('An error occurred during validation. Please try again.');
        setIsProcessingCode(false);
      });
    }
  }, [code, codeProcessed, isProcessingCode, router, codeValidateRequest]);

  if (codeValidateRequest.isLoading || isProcessingCode) {
    return (
      <Box
        sx={{
          height: '100vh',
          width: '100vw',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)',
          overflow: 'hidden',
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: '100vh',
        width: '100vw',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #1e3a8a 0%, #f7c873 100%)',
        overflow: 'hidden',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
      }}
    >
      <Container maxWidth="sm" disableGutters>
        <Paper
          elevation={6}
          sx={{
            p: { xs: 3, sm: 5 },
            borderRadius: 4,
            boxShadow: '0 8px 32px 0 rgba(30,58,138,0.2)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: { xs: '90vw', sm: 400 },
            maxWidth: 420,
            mx: 'auto',
          }}
        >
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
            <Image
              src="/CO-gold.svg"
              alt="US Chamber CO Logo"
              width={120}
              height={56}
              style={{ objectFit: 'contain' }}
            />
          </Box>
          <Typography variant="h4" component="h1" gutterBottom align="center" sx={{ fontWeight: 700, color: '#1e3a8a' }}>
            Welcome to CO
          </Typography>
          <Typography variant="subtitle1" align="center" sx={{ mb: 2, color: '#555' }}>
            Member Dashboard Login
          </Typography>

          {(loginRequest.isError || codeValidateRequest.isError) && (
            <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
              {loginRequest.error?.message || codeValidateRequest.error?.message || 'An error occurred'}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2, width: '100%' }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="loginemail"
              label="Email Address"
              name="loginemail"
              autoComplete="email"
              autoFocus
              value={values.loginemail}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.loginemail && Boolean(errors.loginemail)}
              helperText={touched.loginemail && errors.loginemail}
              sx={{ background: '#fafbfc', borderRadius: 1 }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              autoComplete="current-password"
              value={values.password}
              onChange={handleChange}
              onBlur={handleBlur}
              error={touched.password && Boolean(errors.password)}
              helperText={touched.password && errors.password}
              sx={{ background: '#fafbfc', borderRadius: 1 }}
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              disabled={!dirty || !isValid || loginRequest.isPending}
              sx={{
                mt: 3,
                mb: 2,
                fontWeight: 700,
                background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                color: '#fff',
                boxShadow: '0 2px 8px 0 rgba(30,58,138,0.10)',
                '&:hover': {
                  background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
                  opacity: 0.95,
                },
                '&:disabled': {
                  opacity: 0.6,
                },
              }}
            >
              {loginRequest.isPending ? 'Signing in...' : 'Sign in'}
            </Button>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="textSecondary">
                OR
              </Typography>
            </Divider>

            {/* Social Login Buttons */}
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                onClick={() => handleSocialLogin('google')}
                disabled={socialLoginRequest.isPending}
                sx={{
                  flex: 1,
                  minWidth: '100px',
                  borderColor: '#db4437',
                  color: '#db4437',
                  '&:hover': {
                    borderColor: '#db4437',
                    backgroundColor: 'rgba(219, 68, 55, 0.04)',
                  },
                }}
              >
                {socialLoginRequest.isPending && socialLoginRequest.variables?.provider === 'google' ? (
                  <CircularProgress size={20} />
                ) : (
                  'Google'
                )}
              </Button>
              <Button
                variant="outlined"
                onClick={() => handleSocialLogin('linkedin')}
                disabled={socialLoginRequest.isPending}
                sx={{
                  flex: 1,
                  minWidth: '100px',
                  borderColor: '#0077b5',
                  color: '#0077b5',
                  '&:hover': {
                    borderColor: '#0077b5',
                    backgroundColor: 'rgba(0, 119, 181, 0.04)',
                  },
                }}
              >
                {socialLoginRequest.isPending && socialLoginRequest.variables?.provider === 'linkedin' ? (
                  <CircularProgress size={20} />
                ) : (
                  'LinkedIn'
                )}
              </Button>
              <Button
                variant="outlined"
                onClick={() => handleSocialLogin('apple')}
                disabled={socialLoginRequest.isPending}
                sx={{
                  flex: 1,
                  minWidth: '100px',
                  borderColor: '#000',
                  color: '#000',
                  '&:hover': {
                    borderColor: '#000',
                    backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  },
                }}
              >
                {socialLoginRequest.isPending && socialLoginRequest.variables?.provider === 'apple' ? (
                  <CircularProgress size={20} />
                ) : (
                  'Apple'
                )}
              </Button>
            </Box>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
} 