import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { signUp } from 'aws-amplify/auth';
import { getCurrentUser, fetchAuthSession } from 'aws-amplify/auth';

// Validation schema for the request body
const adminRegisterSchema = z.object({
  new_admin: z.object({
    email: z.string().email('Invalid email format'),
    username: z.string().min(3, 'Username must be at least 3 characters'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    role: z.enum(['super_admin', 'admin', 'moderator']).optional().default('moderator'),
    firstName: z.string().min(1, 'First name is required').optional(),
    lastName: z.string().min(1, 'Last name is required').optional(),
  }),
  token: z.string().min(1, 'Access token is required'),
});

// Helper function to verify the admin token and get user info
async function verifyAdminToken(token: string) {
  try {
    // In a real implementation, you would verify the JWT token
    // For now, we'll decode it to get user information
    const payload = JSON.parse(atob(token.split('.')[1]));
    
    return {
      isValid: true,
      userId: payload.sub,
      username: payload.username,
      role: payload['custom:role'] || 'admin', // Assuming role is stored in custom attributes
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid token format',
    };
  }
}

// Helper function to check if user has permission to create admin users
function hasCreatePermission(userRole: string, targetRole: string) {
  const roleHierarchy = {
    super_admin: 3,
    admin: 2,
    moderator: 1,
  };
  
  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  const targetLevel = roleHierarchy[targetRole as keyof typeof roleHierarchy] || 0;
  
  return userLevel > targetLevel;
}

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = adminRegisterSchema.parse(body);
    
    const { new_admin, token } = validatedData;
    
    // Verify the admin token
    const tokenVerification = await verifyAdminToken(token);
    if (!tokenVerification.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired token',
          details: tokenVerification.error 
        },
        { status: 401 }
      );
    }
    
    // Check if the current user has permission to create this type of admin
    if (!hasCreatePermission(tokenVerification.role, new_admin.role)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Insufficient permissions to create this admin role',
          details: `${tokenVerification.role} cannot create ${new_admin.role}` 
        },
        { status: 403 }
      );
    }
    
    // Create the new admin user using AWS Cognito
    try {
      const signUpResult = await signUp({
        username: new_admin.username,
        password: new_admin.password,
        options: {
          userAttributes: {
            email: new_admin.email,
            'custom:role': new_admin.role,
            'custom:created_by': tokenVerification.userId,
            'custom:created_by_username': tokenVerification.username,
            'custom:created_at': new Date().toISOString(),
            ...(new_admin.firstName && { given_name: new_admin.firstName }),
            ...(new_admin.lastName && { family_name: new_admin.lastName }),
          },
        },
      });
      
      // Log the admin creation activity
      console.log('Admin user created:', {
        newUserId: signUpResult.userId,
        newUsername: new_admin.username,
        newUserEmail: new_admin.email,
        newUserRole: new_admin.role,
        createdBy: tokenVerification.userId,
        createdByUsername: tokenVerification.username,
        timestamp: new Date().toISOString(),
      });
      
      // Return success response
      return NextResponse.json({
        success: true,
        message: 'Admin user created successfully',
        data: {
          userId: signUpResult.userId,
          username: new_admin.username,
          email: new_admin.email,
          role: new_admin.role,
          status: signUpResult.isSignUpComplete ? 'active' : 'pending_confirmation',
          createdBy: tokenVerification.username,
          createdAt: new Date().toISOString(),
        },
      });
      
    } catch (cognitoError: any) {
      console.error('Cognito signup error:', cognitoError);
      
      // Handle specific Cognito errors
      let errorMessage = 'Failed to create admin user';
      if (cognitoError.name === 'UsernameExistsException') {
        errorMessage = 'Username already exists';
      } else if (cognitoError.name === 'InvalidPasswordException') {
        errorMessage = 'Password does not meet requirements';
      } else if (cognitoError.name === 'InvalidParameterException') {
        errorMessage = 'Invalid user data provided';
      }
      
      return NextResponse.json(
        { 
          success: false, 
          error: errorMessage,
          details: cognitoError.message 
        },
        { status: 400 }
      );
    }
    
  } catch (error: any) {
    console.error('Admin register API error:', error);
    
    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        },
        { status: 400 }
      );
    }
    
    // Handle other errors
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  );
}
