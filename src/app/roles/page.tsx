'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  IconButton,
  Tooltip,

  <PERSON>read<PERSON><PERSON><PERSON>,
  Link,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { RoleTable } from '@/components/roles/RoleTable';
import { RoleForm } from '@/components/roles/RoleForm';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { PageHeader } from '@/components/ui/PageHeader';
import { RoleGuard } from '@/components/ui/RoleGuard';
import { RolesService } from '@/services/roles';
import { Role as RoleType } from '@/types/role';
import AppLayout from '@/layout/AppLayout';

export default function RolesPage() {
  const queryClient = useQueryClient();
  
  // State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleType | null>(null);
  const [deletingRole, setDeletingRole] = useState<RoleType | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // Queries
  const {
    data: roles = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['roles'],
    queryFn: () => RolesService.getRoles(),
  });

  const {
    data: roleHierarchy = [],
  } = useQuery({
    queryKey: ['roleHierarchy'],
    queryFn: () => RolesService.getRoleHierarchy(),
  });

  // Mutations
  const createRoleMutation = useMutation({
    mutationFn: (data: any) => RolesService.createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      queryClient.invalidateQueries({ queryKey: ['roleHierarchy'] });
      setShowAddDialog(false);
      setFormError(null);
    },
    onError: (error: any) => {
      setFormError(error.message || 'Failed to create role');
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => RolesService.updateRole(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      queryClient.invalidateQueries({ queryKey: ['roleHierarchy'] });
      setShowEditDialog(false);
      setEditingRole(null);
      setFormError(null);
    },
    onError: (error: any) => {
      setFormError(error.message || 'Failed to update role');
    },
  });

  const deleteRoleMutation = useMutation({
    mutationFn: (id: string) => RolesService.deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      queryClient.invalidateQueries({ queryKey: ['roleHierarchy'] });
      setShowDeleteDialog(false);
      setDeletingRole(null);
      setSelectedRoles(selectedRoles.filter(id => id !== deletingRole?.id));
    },
    onError: (error: any) => {
      console.error('Failed to delete role:', error);
    },
  });

  const bulkDeleteMutation = useMutation({
    mutationFn: (roleIds: string[]) => RolesService.bulkDeleteRoles(roleIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      queryClient.invalidateQueries({ queryKey: ['roleHierarchy'] });
      setSelectedRoles([]);
    },
    onError: (error: any) => {
      console.error('Failed to delete roles:', error);
    },
  });

  // Filter roles based on search query
  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    role.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle role edit
  const handleEditRole = (role: RoleType) => {
    setEditingRole(role);
    setShowEditDialog(true);
    setFormError(null);
  };

  // Handle role delete
  const handleDeleteRole = (role: RoleType) => {
    setDeletingRole(role);
    setShowDeleteDialog(true);
  };

  // Handle assign permissions (placeholder for future implementation)
  const handleAssignPermissions = (role: RoleType) => {
    // This would open a permissions management dialog
    console.log('Assign permissions for role:', role.name);
  };

  // Handle form submission
  const handleFormSubmit = async (data: any) => {
    if (editingRole) {
      await updateRoleMutation.mutateAsync({ id: editingRole.id, data });
    } else {
      await createRoleMutation.mutateAsync(data);
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedRoles.length > 0) {
      await bulkDeleteMutation.mutateAsync(selectedRoles);
    }
  };

  // Get role statistics
  const getRoleStats = () => {
    const totalRoles = roles.length;
    const systemRoles = roles.filter(role => role.isSystem).length;
    const customRoles = totalRoles - systemRoles;
    const totalMembers = roles.reduce((sum, role) => sum + (role.memberCount || 0), 0);

    return { totalRoles, systemRoles, customRoles, totalMembers };
  };

  const stats = getRoleStats();

  return (
    <AppLayout>
      <Box sx={{ p: 3 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 3 }}>
          <Link href="/dashboard" color="inherit" underline="hover">
            Dashboard
          </Link>
          <Typography color="text.primary">Roles</Typography>
        </Breadcrumbs>

      {/* Page Header */}
      <PageHeader
        title="Role Management"
        subtitle="Manage user roles and permissions"
        icon={<SecurityIcon />}
        actions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => refetch()}
              disabled={isLoading}
            >
              Refresh
            </Button>
            <RoleGuard requiredPermissions={['roles.create']}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setShowAddDialog(true);
                  setFormError(null);
                }}
              >
                Add Role
              </Button>
            </RoleGuard>
          </Box>
        }
      />

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load roles. Please try again.
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SecurityIcon color="primary" />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalRoles}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Roles
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUpIcon color="success" />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.customRoles}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Custom Roles
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SecurityIcon color="warning" />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.systemRoles}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    System Roles
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <PeopleIcon color="info" />
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.totalMembers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Members
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Actions */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <TextField
          placeholder="Search roles..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ minWidth: 300, flexGrow: 1 }}
        />
        
        {selectedRoles.length > 0 && (
          <RoleGuard requiredPermissions={['roles.delete']}>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
            >
              Delete Selected ({selectedRoles.length})
            </Button>
          </RoleGuard>
        )}
      </Box>

      {/* Role Hierarchy Display */}
      {roleHierarchy.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Role Hierarchy
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {roleHierarchy.map((role, index) => (
              <Chip
                key={role.id}
                label={`${role.name} (Level ${role.level})`}
                color={role.level >= 8 ? 'error' : role.level >= 5 ? 'warning' : 'info'}
                variant="outlined"
                size="small"
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Roles Table */}
      <RoleTable
        roles={filteredRoles}
        onEdit={handleEditRole}
        onDelete={handleDeleteRole}
        onAssignPermissions={handleAssignPermissions}
        selectedRoles={selectedRoles}
        onSelectionChange={setSelectedRoles}
        loading={isLoading}
        currentUserRole="admin" // This should come from auth context
      />

      {/* Add Role Dialog */}
      <Dialog
        open={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add New Role</DialogTitle>
        <DialogContent>
          <RoleForm
            onSubmit={handleFormSubmit}
            onCancel={() => setShowAddDialog(false)}
            loading={createRoleMutation.isPending}
            error={formError}
            currentUserRole="admin"
          />
        </DialogContent>
      </Dialog>

      {/* Edit Role Dialog */}
      <Dialog
        open={showEditDialog}
        onClose={() => setShowEditDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Edit Role</DialogTitle>
        <DialogContent>
          {editingRole && (
            <RoleForm
              role={editingRole}
              onSubmit={handleFormSubmit}
              onCancel={() => setShowEditDialog(false)}
              loading={updateRoleMutation.isPending}
              error={formError}
              currentUserRole="admin"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Role"
        message={
          deletingRole
            ? `Are you sure you want to delete the role "${deletingRole.name}"? This action cannot be undone.`
            : ''
        }
        onConfirm={() => {
          if (deletingRole) {
            deleteRoleMutation.mutate(deletingRole.id);
          }
        }}
        onCancel={() => setShowDeleteDialog(false)}
        loading={deleteRoleMutation.isPending}
        confirmText="Delete"
        cancelText="Cancel"
        severity="error"
      />
    </Box>
    </AppLayout>
  );
} 