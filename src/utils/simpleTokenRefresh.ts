import { fetchAuthSession } from 'aws-amplify/auth';
import jwt from 'jsonwebtoken';
import { getCognitoTokensFromCookies, syncCognitoTokensToCookies, getCognitoCurrentUser } from './auth';

// Check if access token is expired
export const isAccessTokenExpired = (accessToken: string): boolean => {
  try {
    const decoded = jwt.decode(accessToken, { complete: true });
    if (!decoded || typeof decoded === 'string' || !decoded.payload) {
      return true;
    }

    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    console.error('Error checking token expiry:', error);
    return true;
  }
};

// Simple function to refresh tokens if needed
export const refreshTokenIfNeeded = async (): Promise<boolean> => {
  try {
    // Get tokens from cookies
    const tokens = getCognitoTokensFromCookies();
    
    // If no access token, user needs to login
    if (!tokens.accessToken) {
      console.log('No access token found');
      return false;
    }

    // Check if access token is expired
    if (!isAccessTokenExpired(tokens.accessToken)) {
      console.log('Access token is still valid');
      return true;
    }

    console.log('Access token expired, refreshing...');

    // Use Amplify's fetchAuthSession to refresh tokens
    const session = await fetchAuthSession({ forceRefresh: true });

    if (!session.tokens || !session.tokens.accessToken) {
      console.error('Failed to refresh tokens');
      return false;
    }

    // Get current user to sync new tokens
    const currentUser = getCognitoCurrentUser();
    if (!currentUser) {
      console.error('No current user found');
      return false;
    }

    // Sync new tokens to cookies
    const syncSuccess = syncCognitoTokensToCookies(currentUser);
    
    if (syncSuccess) {
      console.log('✅ Tokens refreshed and synced successfully');
      return true;
    } else {
      console.error('Failed to sync refreshed tokens');
      return false;
    }

  } catch (error: any) {
    console.error('Token refresh failed:', error);
    
    // If refresh fails due to invalid refresh token, user needs to login again
    if (error.name === 'NotAuthorizedException' || 
        error.message?.includes('refresh token') ||
        error.message?.includes('not authorized')) {
      console.log('Refresh token invalid, user needs to login again');
      return false;
    }
    
    return false;
  }
};

// Check authentication and refresh if needed
export const checkAuthAndRefresh = async (): Promise<boolean> => {
  try {
    const tokens = getCognitoTokensFromCookies();
    
    // No tokens means user is not authenticated
    if (!tokens.accessToken || !tokens.idToken) {
      return false;
    }

    // If access token is valid, user is authenticated
    if (!isAccessTokenExpired(tokens.accessToken)) {
      return true;
    }

    // Try to refresh tokens
    return await refreshTokenIfNeeded();
    
  } catch (error) {
    console.error('Auth check failed:', error);
    return false;
  }
};
