export const AUTH_CONSTANTS = {
  STORAGE_KEYS: {
    PENDING_TOTP_SETUP: 'pendingTOTPSetup',
  },
  ROUTES: {
    DASHBOARD: '/dashboard',
    LOGIN: '/login',
  },
  TIMEOUTS: {
    SESSION_WAIT: 2000,
    ERROR_REDIRECT: 3000,
  },
  QR_CODE: {
    SIZE: 200,
    ISSUER: 'CO',
  },
  FORM_VALIDATION: {
    MFA_CODE_LENGTH: 6,
    MFA_CODE_PATTERN: '[0-9]*',
  },
  MESSAGES: {
    TOTP_SETUP_INSTRUCTION: 'Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)',
    TOTP_MANUAL_ENTRY: 'Can\'t scan? Enter this code manually:',
    REMEMBER_DEVICE: 'Remember this device for 30 days',
    TOTP_SETUP_FAILED: 'TOTP setup failed. You can enable two-factor authentication later in your account settings.',
    TOTP_SETUP_SUCCESS: 'Please set up your authenticator app by scanning the QR code below.',
    REGISTRATION_PROCESSING: 'Completing your registration. Please wait...',
    REGISTRATION_PROCESSING_SUBTITLE: 'This may take a few moments...',
  },
  PAGE_TITLES: {
    WELCOME: 'Welcome to CO',
    TOTP_SETUP: 'Set up Two-Factor Authentication',
    MFA_TOTP: 'Enter your Authenticator App code',
    MFA_SMS: 'Enter the SMS code sent to your phone',
    MEMBER_LOGIN: 'Member Dashboard Login',
    REGISTRATION_COMPLETING: 'Completing your registration...',
  },
} as const;
