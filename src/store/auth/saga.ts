import * as authAPI from '@/services/authAPI';
import * as loginAPI from '@/services/loginAPI';
import { LoginFormValues, SocialLoginRequest } from '@/services/loginAPI';
import { LoginFormData, User } from '@/types/auth';
import { PayloadAction } from '@reduxjs/toolkit';
import { all, call, fork, put, takeLatest } from 'redux-saga/effects';
import {
    codeValidationFailure,
    codeValidationRequest,
    codeValidationSuccess,
    completeMFAFailure,
    completeMFARequest,
    completeMFASuccess,
    loginFailure,
    loginFormFailure,
    loginFormRequest,
    loginFormSuccess,
    loginMFARequired,
    loginRequest,
    loginSuccess,
    logoutFailure,
    logoutRequest,
    logoutSuccess,
    refreshUserFailure,
    refreshUserRequest,
    refreshUserSuccess,
    socialLoginFailure,
    socialLoginRequest,
    socialLoginSuccess,
} from './redux';

// Login saga
function* loginSaga(action: PayloadAction<LoginFormData>) {
  try {
    const response: { user?: User; requiresMFA?: boolean; challengeType?: 'SMS' | 'TOTP' } = yield call(
      authAPI.login,
      action.payload
    );

    if (response.requiresMFA) {
      yield put(loginMFARequired({ challengeType: response.challengeType || 'TOTP' }));
    } else if (response.user) {
      yield put(loginSuccess(response.user));
    }
  } catch (error: any) {
    yield put(loginFailure(error.message || 'Login failed'));
  }
}

// Login form saga for TanStack Query integration
function* loginFormSaga(action: PayloadAction<LoginFormValues>) {
  try {
    const response: loginAPI.LoginResponse = yield call(loginAPI.loginUser, action.payload);
    
    if (response.success && response.token) {
      yield put(loginFormSuccess({ token: response.token }));
    } else {
      yield put(loginFormFailure(response.message || 'Login failed'));
    }
  } catch (error: any) {
    yield put(loginFormFailure(error.message || 'Login failed'));
  }
}

// Social login saga
function* socialLoginSaga(action: PayloadAction<SocialLoginRequest>) {
  try {
    const response: loginAPI.SocialLoginResponse = yield call(loginAPI.socialLogin, action.payload);
    
    if (response.success && response.url) {
      yield put(socialLoginSuccess({ url: response.url }));
    } else {
      yield put(socialLoginFailure('Social login failed'));
    }
  } catch (error: any) {
    yield put(socialLoginFailure(error.message || 'Social login failed'));
  }
}

// Code validation saga
function* codeValidationSaga(action: PayloadAction<string>) {
  try {
    const response: loginAPI.CodeValidationResponse = yield call(loginAPI.validateCode, action.payload);
    
    if (response.success && response.token) {
      yield put(codeValidationSuccess({ token: response.token }));
    } else {
      yield put(codeValidationFailure('Code validation failed'));
    }
  } catch (error: any) {
    yield put(codeValidationFailure(error.message || 'Code validation failed'));
  }
}

// Complete MFA saga
function* completeMFASaga(action: PayloadAction<{ code: string; rememberDevice?: boolean }>) {
  try {
    const user: User = yield call(authAPI.completeMFA, action.payload.code, action.payload.rememberDevice);
    yield put(completeMFASuccess(user));
  } catch (error: any) {
    yield put(completeMFAFailure(error.message || 'MFA verification failed'));
  }
}

// Logout saga
function* logoutSaga() {
  try {
    yield call(authAPI.logout);
    yield put(logoutSuccess());
  } catch (error: any) {
    yield put(logoutFailure(error.message || 'Logout failed'));
  }
}

// Refresh user saga
function* refreshUserSaga() {
  try {
    const user: User = yield call(authAPI.refreshUser);
    yield put(refreshUserSuccess(user));
  } catch (error: any) {
    yield put(refreshUserFailure(error.message || 'Failed to refresh user'));
  }
}

// Root auth saga
export function* authSaga() {
  yield takeLatest(loginRequest.type, loginSaga);
  yield takeLatest(loginFormRequest.type, loginFormSaga);
  yield takeLatest(socialLoginRequest.type, socialLoginSaga);
  yield takeLatest(codeValidationRequest.type, codeValidationSaga);
  yield takeLatest(completeMFARequest.type, completeMFASaga);
  yield takeLatest(logoutRequest.type, logoutSaga);
  yield takeLatest(refreshUserRequest.type, refreshUserSaga);
}

export function* rootSaga() {
  yield all([
    fork(authSaga),
    // Add other sagas here as you create them
  ]);
}

