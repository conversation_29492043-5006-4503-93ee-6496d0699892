import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getAuthToken } from './utils/auth';

const PUBLIC_ROUTES = ['/', '/register', '/admin', '/forgot-password', '/login', '/adminRegister', '/totp-setup'];

function isAuthenticated(request: NextRequest): boolean {
  const token =
    request.cookies.get('token')?.value ||
    getAuthToken()||
    request.cookies.get('authToken')?.value;
console.log(token, 'token in middleware');
  return !!token && token.trim() !== '' && token !== 'undefined' && token !== 'null';
}

function getCleanPath(pathname: string): string {
  return pathname.replace(/\/+$/, '') || '/';
}

export function middleware(request: NextRequest) {
  const cleanPath = getCleanPath(request.nextUrl.pathname);
  const userIsAuthenticated = isAuthenticated(request);

  console.log('Middleware - Path:', cleanPath);
  console.log('Middleware - User authenticated:', userIsAuthenticated);

  // Skip middleware for static and internal files
  if (
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.match(/\.(ico|png|jpg|js|css|svg)$/)
  ) {
    return NextResponse.next();
  }

  const isPublicRoute = PUBLIC_ROUTES.includes(cleanPath);

  if (!userIsAuthenticated && !isPublicRoute) {
    const loginUrl = new URL('/', request.url);
    loginUrl.searchParams.set('redirect', cleanPath);
    console.log('Middleware - Redirecting unauthenticated user to login');
    return NextResponse.redirect(loginUrl);
  }

  if (userIsAuthenticated && isPublicRoute) {
    const redirectTo = request.nextUrl.searchParams.get('redirect');
    const target = redirectTo && redirectTo.startsWith('/') ? redirectTo : '/dashboard';
    console.log('Middleware - Redirecting authenticated user from public route to:', target);
    return NextResponse.redirect(new URL(target, request.url));
  }

  console.log('Middleware - Allowing access to:', cleanPath);
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon\\.ico).*)'],
};
