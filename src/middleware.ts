import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

const PUBLIC_ROUTES = ['/', '/register', '/admin', '/forgot-password', '/login', '/adminRegister'];

// Check if access token is expired
function isAccessTokenExpired(accessToken: string): boolean {
  try {
    if (!accessToken || accessToken.trim() === '') {
      return true;
    }

    const decoded = jwt.decode(accessToken, { complete: true });
    if (!decoded || typeof decoded === 'string' || !decoded.payload) {
      return true;
    }
    console.log(decoded,'decoded')
    const payload = decoded.payload as any;
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= payload.exp;
  } catch (error) {
    return true;
  }
}

function isAuthenticated(request: NextRequest): { isAuth: boolean; needsRefresh: boolean } {
  // Get Cognito tokens from cookies
  const accessToken = request.cookies.get('cognito_access_token')?.value;
  const idToken = request.cookies.get('cognito_id_token')?.value;
  const refreshToken = request.cookies.get('cognito_refresh_token')?.value;

  // If no access token, user is not authenticated
  if (!accessToken || !idToken) {
    return { isAuth: false, needsRefresh: false };
  }

  // Check if access token is expired
  const isExpired = isAccessTokenExpired(accessToken);

  if (isExpired) {
    // If expired but has refresh token, needs refresh
    if (refreshToken) {
      return { isAuth: false, needsRefresh: true };
    }
    // If expired and no refresh token, not authenticated
    return { isAuth: false, needsRefresh: false };
  }

  // Token is valid
  return { isAuth: true, needsRefresh: false };
}

function getCleanPath(pathname: string): string {
  return pathname.replace(/\/+$/, '') || '/';
}

export function middleware(request: NextRequest) {
  const cleanPath = getCleanPath(request.nextUrl.pathname);

  // Skip middleware for static and internal files
  if (
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.match(/\.(ico|png|jpg|js|css|svg)$/)
  ) {
    return NextResponse.next();
  }

  const authStatus = isAuthenticated(request);
  const isPublicRoute = PUBLIC_ROUTES.includes(cleanPath);

  console.log('Middleware - Path:', cleanPath);
  console.log('Middleware - Auth status:', authStatus);

  // If user needs token refresh, allow access but set a header to trigger client-side refresh
  if (authStatus.needsRefresh && !isPublicRoute) {
    console.log('Middleware - Token needs refresh, setting refresh header');
    const response = NextResponse.next();
    response.headers.set('X-Token-Refresh-Needed', 'true');
    return response;
  }

  // If not authenticated and trying to access protected route
  if (!authStatus.isAuth && !isPublicRoute) {
    const loginUrl = new URL('/', request.url);
    loginUrl.searchParams.set('redirect', cleanPath);
    console.log('Middleware - Redirecting unauthenticated user to login');
    return NextResponse.redirect(loginUrl);
  }

  // If authenticated and trying to access public route
  if (authStatus.isAuth && isPublicRoute) {
    const redirectTo = request.nextUrl.searchParams.get('redirect');
    const target = redirectTo && redirectTo.startsWith('/') ? redirectTo : '/dashboard';
    console.log('Middleware - Redirecting authenticated user from public route to:', target);
    return NextResponse.redirect(new URL(target, request.url));
  }

  console.log('Middleware - Allowing access to:', cleanPath);
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon\\.ico).*)'],
};
