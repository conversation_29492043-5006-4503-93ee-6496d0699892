import { z } from 'zod';

// Base analytics response schema
export const analyticsResponseSchema = z.object({
  success: z.boolean(),
  data: z.any(),
  message: z.string().optional(),
  timestamp: z.string(),
});

// Chart data interfaces
export interface ChartDataPoint {
  label: string;
  value: number;
  percentage?: number;
  color?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

// Member Growth Analytics
export interface MemberGrowthDataPoint {
  period: string;
  newMembers: number;
  totalMembers: number;
  growthRate: number;
  churnRate: number;
}

export interface MemberGrowthAnalytics {
  timeSeries: TimeSeriesDataPoint[];
  monthlyGrowth: MemberGrowthDataPoint[];
  yearlyGrowth: MemberGrowthDataPoint[];
  growthTrend: 'increasing' | 'decreasing' | 'stable';
  averageGrowthRate: number;
}

// Membership Tier Analytics
export interface MembershipTierData {
  tier: 'basic' | 'premium' | 'enterprise' | 'vip';
  count: number;
  percentage: number;
  revenue: number;
  averageLifetime: number;
}

export interface MembershipTierAnalytics {
  distribution: MembershipTierData[];
  totalRevenue: number;
  averageRevenuePerMember: number;
  tierConversionRates: {
    basicToPremium: number;
    premiumToEnterprise: number;
    enterpriseToVip: number;
  };
}

// Community Status Analytics
export interface CommunityStatusData {
  status: 'unverified' | 'verified' | 'pending' | 'rejected';
  count: number;
  percentage: number;
  averageVerificationTime: number;
}

export interface CommunityStatusAnalytics {
  statusBreakdown: CommunityStatusData[];
  verificationMetrics: {
    totalVerified: number;
    verificationRate: number;
    averageVerificationTime: number;
    pendingVerifications: number;
  };
}

// Verification Status Analytics
export interface VerificationStatusData {
  status: 'requires_review' | 'in_progress' | 'completed' | 'rejected';
  count: number;
  percentage: number;
  averageProcessingTime: number;
}

export interface VerificationStatusAnalytics {
  statusBreakdown: VerificationStatusData[];
  processingMetrics: {
    totalCompleted: number;
    completionRate: number;
    averageProcessingTime: number;
    backlogSize: number;
  };
}

// Blacklist Report Analytics
export interface BlacklistReportData {
  reportId: string;
  memberId: number;
  memberName: string;
  reportType: 'spam' | 'disposable_email' | 'suspicious_activity' | 'manual_flag';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'resolved' | 'false_positive';
  createdAt: string;
  resolvedAt?: string;
  description: string;
  evidence: string[];
}

export interface BlacklistStatistics {
  totalReports: number;
  activeReports: number;
  resolvedReports: number;
  falsePositives: number;
  spamReports: number;
  disposableEmailReports: number;
  suspiciousActivityReports: number;
  manualFlags: number;
}

export interface BlacklistAnalytics {
  statistics: BlacklistStatistics;
  reportsByType: ChartDataPoint[];
  reportsBySeverity: ChartDataPoint[];
  reportsByStatus: ChartDataPoint[];
  timeSeriesData: TimeSeriesDataPoint[];
  riskAssessment: {
    overallRisk: 'low' | 'medium' | 'high';
    riskScore: number;
    riskFactors: string[];
    recommendations: string[];
  };
}

// Feature Flag Analytics
export interface FeatureFlagData {
  featureHandle: string;
  featureName: string;
  enabledCount: number;
  totalCount: number;
  adoptionRate: number;
  usageByTier: {
    tier: string;
    enabledCount: number;
    totalCount: number;
    adoptionRate: number;
  }[];
}

export interface FeatureFlagAnalytics {
  features: FeatureFlagData[];
  overallAdoptionRate: number;
  adoptionByTier: ChartDataPoint[];
  featurePerformance: {
    featureHandle: string;
    featureName: string;
    performance: 'excellent' | 'good' | 'average' | 'poor';
    metrics: {
      engagement: number;
      retention: number;
      satisfaction: number;
    };
  }[];
}

// A/B Testing Analytics
export interface ABTestData {
  testId: string;
  testName: string;
  variant: 'control' | 'treatment';
  participants: number;
  conversions: number;
  conversionRate: number;
  confidence: number;
  isSignificant: boolean;
}

export interface ABTestingAnalytics {
  activeTests: ABTestData[];
  completedTests: ABTestData[];
  overallResults: {
    totalTests: number;
    significantResults: number;
    averageLift: number;
  };
}

// Export types
export interface AnalyticsExportOptions {
  format: 'csv' | 'pdf' | 'json';
  dataType: 'member_growth' | 'membership_tiers' | 'community_status' | 'verification_status' | 'blacklist_reports' | 'feature_flags' | 'all';
  dateRange?: {
    start: string;
    end: string;
  };
  filters?: Record<string, any>;
}

export interface AnalyticsExportResult {
  filename: string;
  downloadUrl: string;
  size: number;
  format: string;
}

// Analytics Dashboard Summary
export interface AnalyticsDashboardSummary {
  memberGrowth: MemberGrowthAnalytics;
  membershipTiers: MembershipTierAnalytics;
  communityStatus: CommunityStatusAnalytics;
  verificationStatus: VerificationStatusAnalytics;
  blacklistReports: BlacklistAnalytics;
  featureFlags: FeatureFlagAnalytics;
  abTesting: ABTestingAnalytics;
  lastUpdated: string;
}

// Zod schemas for validation
export const memberGrowthDataSchema = z.object({
  period: z.string(),
  newMembers: z.number(),
  totalMembers: z.number(),
  growthRate: z.number(),
  churnRate: z.number(),
});

export const blacklistReportDataSchema = z.object({
  reportId: z.string(),
  memberId: z.number(),
  memberName: z.string(),
  reportType: z.enum(['spam', 'disposable_email', 'suspicious_activity', 'manual_flag']),
  severity: z.enum(['low', 'medium', 'high', 'critical']),
  status: z.enum(['active', 'resolved', 'false_positive']),
  createdAt: z.string(),
  resolvedAt: z.string().optional(),
  description: z.string(),
  evidence: z.array(z.string()),
});

export const featureFlagDataSchema = z.object({
  featureHandle: z.string(),
  featureName: z.string(),
  enabledCount: z.number(),
  totalCount: z.number(),
  adoptionRate: z.number(),
  usageByTier: z.array(z.object({
    tier: z.string(),
    enabledCount: z.number(),
    totalCount: z.number(),
    adoptionRate: z.number(),
  })),
});

// Type exports
export type MemberGrowthDataPoint1 = z.infer<typeof memberGrowthDataSchema>;
export type BlacklistReportDataPoint = z.infer<typeof blacklistReportDataSchema>;
export type FeatureFlagDataPoint = z.infer<typeof featureFlagDataSchema>; 