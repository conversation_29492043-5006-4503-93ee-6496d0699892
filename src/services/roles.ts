import { Role, CreateRoleData, UpdateRoleData, SYSTEM_PERMISSIONS, DEFAULT_ROLES } from '@/types/role';

// Mock data for development
let mockRoles: Role[] = DEFAULT_ROLES.map((role, index) => ({
  ...role,
  id: `role-${index + 1}`,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  memberCount: Math.floor(Math.random() * 50) + 1,
}));

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export class RolesService {
  // Get all roles
  static async getRoles(): Promise<Role[]> {
    await delay(500);
    return [...mockRoles];
  }

  // Get role by ID
  static async getRoleById(id: string): Promise<Role | null> {
    await delay(200);
    return mockRoles.find(role => role.id === id) || null;
  }

  // Create new role
  static async createRole(data: CreateRoleData): Promise<Role> {
    await delay(800);
    
    // Validate role name uniqueness
    if (mockRoles.some(role => role.name.toLowerCase() === data.name.toLowerCase())) {
      throw new Error('Role name already exists');
    }

    // Validate level
    if (data.level < 1 || data.level > 10) {
      throw new Error('Role level must be between 1 and 10');
    }

    // Get permissions from IDs
    const permissions = SYSTEM_PERMISSIONS.filter(permission => 
      data.permissions.includes(permission.id)
    );

    const newRole: Role = {
      id: `role-${Date.now()}`,
      name: data.name,
      description: data.description,
      level: data.level,
      permissions,
      isSystem: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      memberCount: 0,
    };

    mockRoles.push(newRole);
    return newRole;
  }

  // Update role
  static async updateRole(id: string, data: UpdateRoleData): Promise<Role> {
    await delay(600);
    
    const roleIndex = mockRoles.findIndex(role => role.id === id);
    if (roleIndex === -1) {
      throw new Error('Role not found');
    }

    const role = mockRoles[roleIndex];
    
    // Prevent modification of system roles
    if (role.isSystem) {
      throw new Error('Cannot modify system roles');
    }

    // Validate role name uniqueness if name is being changed
    if (data.name && data.name !== role.name) {
      if (mockRoles.some(r => r.id !== id && r.name.toLowerCase() === data.name!.toLowerCase())) {
        throw new Error('Role name already exists');
      }
    }

    // Validate level
    if (data.level && (data.level < 1 || data.level > 10)) {
      throw new Error('Role level must be between 1 and 10');
    }

    // Get permissions from IDs if provided
    let permissions = role.permissions;
    if (data.permissions) {
      permissions = SYSTEM_PERMISSIONS.filter(permission => 
        data.permissions!.includes(permission.id)
      );
    }

    const updatedRole: Role = {
      ...role,
      name: data.name || role.name,
      description: data.description || role.description,
      level: data.level || role.level,
      permissions,
      updatedAt: new Date().toISOString(),
    };

    mockRoles[roleIndex] = updatedRole;
    return updatedRole;
  }

  // Delete role
  static async deleteRole(id: string): Promise<void> {
    await delay(400);
    
    const roleIndex = mockRoles.findIndex(role => role.id === id);
    if (roleIndex === -1) {
      throw new Error('Role not found');
    }

    const role = mockRoles[roleIndex];
    
    // Prevent deletion of system roles
    if (role.isSystem) {
      throw new Error('Cannot delete system roles');
    }

    // Check if role has members
    if (role.memberCount && role.memberCount > 0) {
      throw new Error('Cannot delete role that has assigned members');
    }

    mockRoles.splice(roleIndex, 1);
  }

  // Get all permissions
  static async getPermissions() {
    await delay(200);
    return SYSTEM_PERMISSIONS;
  }

  // Get permissions by category
  static async getPermissionsByCategory() {
    await delay(200);
    const categories = [...new Set(SYSTEM_PERMISSIONS.map(p => p.category))];
    return categories.map(category => ({
      category,
      permissions: SYSTEM_PERMISSIONS.filter(p => p.category === category)
    }));
  }

  // Assign role to user (mock implementation)
  static async assignRoleToUser(roleId: string, userId: string): Promise<void> {
    await delay(300);
    
    const role = mockRoles.find(r => r.id === roleId);
    if (!role) {
      throw new Error('Role not found');
    }

    // In a real implementation, this would update the user's role
    // and increment the memberCount for the role
    role.memberCount = (role.memberCount || 0) + 1;
  }

  // Remove role from user (mock implementation)
  static async removeRoleFromUser(roleId: string, userId: string): Promise<void> {
    await delay(300);
    
    const role = mockRoles.find(r => r.id === roleId);
    if (!role) {
      throw new Error('Role not found');
    }

    // In a real implementation, this would update the user's role
    // and decrement the memberCount for the role
    if (role.memberCount && role.memberCount > 0) {
      role.memberCount -= 1;
    }
  }

  // Get role hierarchy
  static async getRoleHierarchy(): Promise<Role[]> {
    await delay(300);
    return [...mockRoles].sort((a, b) => b.level - a.level);
  }

  // Search roles
  static async searchRoles(query: string): Promise<Role[]> {
    await delay(400);
    const lowercaseQuery = query.toLowerCase();
    return mockRoles.filter(role => 
      role.name.toLowerCase().includes(lowercaseQuery) ||
      role.description.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Get roles by level range
  static async getRolesByLevel(minLevel: number, maxLevel: number): Promise<Role[]> {
    await delay(300);
    return mockRoles.filter(role => role.level >= minLevel && role.level <= maxLevel);
  }

  // Bulk operations
  static async bulkDeleteRoles(roleIds: string[]): Promise<void> {
    await delay(1000);
    
    for (const id of roleIds) {
      const role = mockRoles.find(r => r.id === id);
      if (role?.isSystem) {
        throw new Error(`Cannot delete system role: ${role.name}`);
      }
      if (role?.memberCount && role.memberCount > 0) {
        throw new Error(`Cannot delete role with assigned members: ${role.name}`);
      }
    }

    mockRoles = mockRoles.filter(role => !roleIds.includes(role.id));
  }

  // Reset mock data (for development)
  static async resetMockData(): Promise<void> {
    await delay(200);
    mockRoles = DEFAULT_ROLES.map((role, index) => ({
      ...role,
      id: `role-${index + 1}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      memberCount: Math.floor(Math.random() * 50) + 1,
    }));
  }
} 