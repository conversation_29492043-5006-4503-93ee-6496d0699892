import { LoginFormData, User, UserRole, Permission } from '@/types/auth';

// Mock API service - replace with your actual API calls
export const login = async (credentials: LoginFormData): Promise<{ user?: User; requiresMFA?: boolean; challengeType?: 'SMS' | 'TOTP' }> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock login logic - replace with actual API call
  if (credentials.username === '<EMAIL>' && credentials.password === 'password') {
    return {
      user: {
        id: '1',
        email: '<EMAIL>',
        username: '<EMAIL>',
        firstName: 'Demo',
        lastName: 'User',
        roles: [UserRole.MEMBER],
        permissions: [Permission.READ_DASHBOARD],
        mfaEnabled: false,
        emailVerified: true,
        createdAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
      }
    };
  } else if (credentials.username === '<EMAIL>') {
    return {
      requiresMFA: true,
      challengeType: 'TOTP'
    };
  } else {
    throw new Error('Invalid credentials');
  }
};

export const completeMFA = async (code: string, rememberDevice?: boolean): Promise<User> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Mock MFA verification - replace with actual API call
  if (code === '123456') {
    return {
      id: '2',
      email: '<EMAIL>',
      username: '<EMAIL>',
      firstName: 'MFA',
      lastName: 'User',
      roles: [UserRole.MEMBER],
      permissions: [Permission.READ_DASHBOARD],
      mfaEnabled: true,
      emailVerified: true,
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
    };
  } else {
    throw new Error('Invalid MFA code');
  }
};

export const logout = async (): Promise<void> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock logout logic - replace with actual API call
  // Clear tokens, etc.
};

export const refreshUser = async (): Promise<User> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock refresh user logic - replace with actual API call
  return {
    id: '1',
    email: '<EMAIL>',
    username: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    roles: [UserRole.MEMBER],
    permissions: [Permission.READ_DASHBOARD],
    mfaEnabled: false,
    emailVerified: true,
    createdAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
  };
};
