import {
  Admin<PERSON>ser,
  AdminUserListResponse,
  AdminUserSearchParams,
  AdminUserFilters,
  CreateAdminUserRequest,
  UpdateAdminUserRequest,
  BulkActionRequest,
  AdminUserStats,
  getRolePermissions,
} from "@/types/adminUser";

class AdminUsersService {
  // Mock data - replace with actual API calls
  private mockAdminUsers: AdminUser[] = [
    {
      id: "1",
      username: "john.admin",
      firstName: "<PERSON>",
      lastName: "Administrator",
      email: "<EMAIL>",
      roles: ["super_admin"],
      status: "active",
      permissions: getRolePermissions("SUPER_ADMIN"),
      createdAt: "2024-01-01T10:00:00Z",
      updatedAt: "2024-01-20T14:30:00Z",
      lastLoginAt: "2024-01-20T14:25:00Z",
      phone: "******-0101",
      department: "IT",
      notes: "System administrator with full access",
    },
    {
      id: "2",
      username: "sarah.manager",
      firstName: "<PERSON>",
      lastName: "Manager",
      email: "<EMAIL>",
      roles: ["admin"],
      status: "active",
      permissions: getRolePermissions("ADMIN"),
      createdAt: "2024-01-05T09:15:00Z",
      updatedAt: "2024-01-18T16:45:00Z",
      lastLoginAt: "2024-01-19T11:30:00Z",
      phone: "******-0102",
      department: "Operations",
      notes: "Operations manager",
    },
    {
      id: "3",
      username: "michael.coordinator",
      firstName: "Michael",
      lastName: "Coordinator",
      email: "<EMAIL>",
      roles: ["moderator"],
      status: "active",
      permissions: getRolePermissions("MODERATOR"),
      createdAt: "2024-01-10T14:20:00Z",
      updatedAt: "2024-01-15T10:15:00Z",
      lastLoginAt: "2024-01-20T09:45:00Z",
      phone: "******-0103",
      department: "Member Services",
      notes: "Member services coordinator",
    },
    {
      id: "4",
      username: "emily.assistant",
      firstName: "Emily",
      lastName: "Assistant",
      email: "<EMAIL>",
      roles: ["moderator"],
      status: "pending",
      permissions: getRolePermissions("MODERATOR"),
      createdAt: "2024-01-22T08:30:00Z",
      updatedAt: "2024-01-22T08:30:00Z",
      phone: "******-0104",
      department: "Marketing",
      notes: "Marketing assistant - pending approval",
    },
    {
      id: "5",
      username: "david.analyst",
      firstName: "David",
      lastName: "Analyst",
      email: "<EMAIL>",
      roles: ["admin"],
      status: "inactive",
      permissions: getRolePermissions("ADMIN"),
      createdAt: "2024-01-08T11:45:00Z",
      updatedAt: "2024-01-15T13:20:00Z",
      lastLoginAt: "2024-01-10T15:10:00Z",
      phone: "******-0105",
      department: "Analytics",
      notes: "Data analyst - temporarily inactive",
    },
  ];

  // Get admin users with search, filtering, and pagination
  async getAdminUsers(
    params: AdminUserSearchParams
  ): Promise<AdminUserListResponse> {
    await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API delay

    let filteredUsers = [...this.mockAdminUsers];

    // Apply search filter
    if (params.filters.search) {
      const searchTerm = params.filters.search.toLowerCase();
      filteredUsers = filteredUsers.filter(
        (user) =>
          user.firstName.toLowerCase().includes(searchTerm) ||
          user.lastName.toLowerCase().includes(searchTerm) ||
          user.email.toLowerCase().includes(searchTerm) ||
          user.department?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply status filter
    if (params.filters.status && params.filters.status !== "all") {
      filteredUsers = filteredUsers.filter(
        (user) => user.status === params.filters.status
      );
    }

    // Apply role filter
    if (params.filters.role && params.filters.role !== "all") {
      filteredUsers = filteredUsers.filter(
        (user) =>
          params.filters.role &&
          user.roles.includes(
            params.filters.role as "super_admin" | "admin" | "moderator"
          )
      );
    }

    // Apply department filter
    if (params.filters.department) {
      filteredUsers = filteredUsers.filter(
        (user) => user.department === params.filters.department
      );
    }

    // Apply date range filter
    if (params.filters.dateRange) {
      filteredUsers = filteredUsers.filter((user) => {
        const createdAt = new Date(user.createdAt);
        return (
          createdAt >= params.filters.dateRange!.start &&
          createdAt <= params.filters.dateRange!.end
        );
      });
    }

    // Apply sorting
    if (params.sortBy) {
      filteredUsers.sort((a, b) => {
        const aValue = this.getNestedValue(a, params.sortBy!);
        const bValue = this.getNestedValue(b, params.sortBy!);

        if (aValue < bValue) return params.sortOrder === "desc" ? 1 : -1;
        if (aValue > bValue) return params.sortOrder === "desc" ? -1 : 1;
        return 0;
      });
    }

    // Apply pagination
    const total = filteredUsers.length;
    const startIndex = (params.page - 1) * params.pageSize;
    const endIndex = startIndex + params.pageSize;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    return {
      adminUsers: paginatedUsers,
      total,
      page: params.page,
      pageSize: params.pageSize,
      totalPages: Math.ceil(total / params.pageSize),
    };
  }

  // Get admin user by ID
  async getAdminUser(id: string): Promise<AdminUser | null> {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return this.mockAdminUsers.find((user) => user.id === id) || null;
  }

  // Create new admin user
  async createAdminUser(data: CreateAdminUserRequest): Promise<AdminUser> {
    await new Promise((resolve) => setTimeout(resolve, 300));

    const newUser: AdminUser = {
      id: (this.mockAdminUsers.length + 1).toString(),
      username: data.username,
      firstName: data.firstName || "",
      lastName: data.lastName || "",
      email: data.email,
      roles: [data.roles ? data.roles[0] : "moderator"],
      status: "pending", // New users are pending by default
      permissions: getRolePermissions(
        data.roles ? (data.roles[0].toUpperCase() as any) : "MODERATOR"
      ),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      phone: data.phone,
      department: data.department,
      notes: data.notes,
      createdBy: "current-user-id", // This would come from auth context
    };

    this.mockAdminUsers.push(newUser);
    return newUser;
  }

  // Update admin user
  async updateAdminUser(
    id: string,
    data: UpdateAdminUserRequest
  ): Promise<AdminUser> {
    await new Promise((resolve) => setTimeout(resolve, 300));

    const userIndex = this.mockAdminUsers.findIndex((user) => user.id === id);
    if (userIndex === -1) {
      throw new Error("Admin user not found");
    }

    const updatedUser = {
      ...this.mockAdminUsers[userIndex],
      ...data,
      roles: data.roles ? data.roles : this.mockAdminUsers[userIndex].roles,
      permissions: data.roles
        ? getRolePermissions(data.roles[0].toUpperCase() as any)
        : this.mockAdminUsers[userIndex].permissions,
      updatedAt: new Date().toISOString(),
    };

    this.mockAdminUsers[userIndex] = updatedUser;
    return updatedUser;
  }

  // Delete admin user
  async deleteAdminUser(id: string): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, 200));

    const userIndex = this.mockAdminUsers.findIndex((user) => user.id === id);
    if (userIndex === -1) {
      throw new Error("Admin user not found");
    }

    // Prevent deletion of the last super admin
    const user = this.mockAdminUsers[userIndex];
    if (user.roles.includes("super_admin")) {
      const superAdminCount = this.mockAdminUsers.filter(
        (u) => u.roles.includes("super_admin") && u.status === "active"
      ).length;
      if (superAdminCount <= 1) {
        throw new Error("Cannot delete the last super admin user");
      }
    }

    this.mockAdminUsers.splice(userIndex, 1);
  }

  // Bulk actions
  async bulkAction(request: BulkActionRequest): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, 500));

    switch (request.action) {
      case "delete":
        request.adminUserIds.forEach((id) => {
          const userIndex = this.mockAdminUsers.findIndex(
            (user) => user.id === id
          );
          if (userIndex !== -1) {
            const user = this.mockAdminUsers[userIndex];
            if (user.roles.includes("super_admin")) {
              const superAdminCount = this.mockAdminUsers.filter(
                (u) => u.roles.includes("super_admin") && u.status === "active"
              ).length;
              if (superAdminCount <= 1) {
                throw new Error("Cannot delete the last super admin user");
              }
            }
            this.mockAdminUsers.splice(userIndex, 1);
          }
        });
        break;
      case "activate":
        request.adminUserIds.forEach((id) => {
          const user = this.mockAdminUsers.find((user) => user.id === id);
          if (user) {
            user.status = "active";
            user.updatedAt = new Date().toISOString();
          }
        });
        break;
      case "deactivate":
        request.adminUserIds.forEach((id) => {
          const user = this.mockAdminUsers.find((user) => user.id === id);
          if (user) {
            user.status = "inactive";
            user.updatedAt = new Date().toISOString();
          }
        });
        break;
      case "change_role":
        if (!request.role) {
          throw new Error("Role is required for change_role action");
        }
        request.adminUserIds.forEach((id) => {
          const user = this.mockAdminUsers.find((user) => user.id === id);
          if (user) {
            user.roles = [request.role!];
            user.permissions = getRolePermissions(
              request.role!.toUpperCase() as any
            );
            user.updatedAt = new Date().toISOString();
          }
        });
        break;
    }
  }

  // Get admin user statistics
  async getAdminUserStats(): Promise<AdminUserStats> {
    await new Promise((resolve) => setTimeout(resolve, 200));

    const total = this.mockAdminUsers.length;
    const active = this.mockAdminUsers.filter(
      (u) => u.status === "active"
    ).length;
    const inactive = this.mockAdminUsers.filter(
      (u) => u.status === "inactive"
    ).length;
    const pending = this.mockAdminUsers.filter(
      (u) => u.status === "pending"
    ).length;

    const superAdmins = this.mockAdminUsers.filter((u) =>
      u.roles.includes("super_admin")
    ).length;
    const admins = this.mockAdminUsers.filter((u) =>
      u.roles.includes("admin")
    ).length;
    const moderators = this.mockAdminUsers.filter((u) =>
      u.roles.includes("moderator")
    ).length;

    const thisMonth = new Date();
    thisMonth.setDate(1);
    const newThisMonth = this.mockAdminUsers.filter(
      (u) => new Date(u.createdAt) >= thisMonth
    ).length;

    return {
      total,
      active,
      inactive,
      pending,
      superAdmins,
      admins,
      moderators,
      newThisMonth,
    };
  }

  // Get available departments
  async getDepartments(): Promise<string[]> {
    await new Promise((resolve) => setTimeout(resolve, 100));
    const departments = [
      ...new Set(
        this.mockAdminUsers
          .map((user) => user.department)
          .filter((dept): dept is string => Boolean(dept))
      ),
    ];
    return departments.sort();
  }

  // Check if user can manage another user
  canManageUser(currentUserRole: string, targetUserRole: string): boolean {
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1,
    };

    return (
      roleHierarchy[currentUserRole as keyof typeof roleHierarchy] >
      roleHierarchy[targetUserRole as keyof typeof roleHierarchy]
    );
  }

  // Helper method to get nested object values for sorting
  private getNestedValue(obj: any, path: string): any {
    if (path === "firstName") {
      return `${obj.firstName} ${obj.lastName}`;
    }
    return obj[path];
  }
}

export const adminUsersService = new AdminUsersService();
