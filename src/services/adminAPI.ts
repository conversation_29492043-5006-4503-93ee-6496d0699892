import { getAuthToken, getCognitoTokensFromCookies } from '@/utils/auth';

export interface CreateAdminUserRequest {
  email: string;
  username: string;
  password: string;
  roles?: ('super_admin' | 'admin' | 'moderator')[];
}

export interface CreateAdminUserResponse {
  success: boolean;
  message?: string;
  error?: string;
  details?: string;
  data?: {
    userId: string;
    username: string;
    email: string;
    role: string;
    status: string;
    createdBy: string;
    createdAt: string;
  };
}

/**
 * Get the current user's access token for API calls
 */
function getAccessToken(): string | null {
  // Try to get token from cookies first (synced from Cognito)
  const cognitoTokens = getCognitoTokensFromCookies();
  if (cognitoTokens.accessToken) {
    return cognitoTokens.accessToken;
  }
  
  // Fallback to legacy token
  return getAuthToken();
}

/**
 * Create a new admin user
 */
export async function createAdminUser(userData: CreateAdminUserRequest): Promise<CreateAdminUserResponse> {
  try {
    const accessToken = getAccessToken();
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }
    
    const requestBody = {
      new_admin: {
        email: userData.email,
        username: userData.username,
        password: userData.password,
        roles: userData.roles || ['moderator'],
      },
      token: accessToken,
    };
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify(requestBody),
    });
    
    const result: CreateAdminUserResponse = await response.json();
    
    if (!response.ok) {
      throw new Error(result.error || `HTTP error! status: ${response.status}`);
    }
    
    return result;
    
  } catch (error: any) {
    console.error('Create admin user error:', error);
    
    return {
      success: false,
      error: error.message || 'Failed to create admin user',
      details: error.details || 'An unexpected error occurred',
    };
  }
}

/**
 * Get list of admin users (placeholder for future implementation)
 */
export async function getAdminUsers(): Promise<any> {
  try {
    const accessToken = getAccessToken();
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }
    
    const response = await fetch('/api/admin/users', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error: any) {
    console.error('Get admin users error:', error);
    throw error;
  }
}

/**
 * Update an admin user (placeholder for future implementation)
 */
export async function updateAdminUser(userId: string, userData: Partial<CreateAdminUserRequest>): Promise<any> {
  try {
    const accessToken = getAccessToken();
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }
    
    const response = await fetch(`/api/admin/users/${userId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        updates: userData,
        token: accessToken,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error: any) {
    console.error('Update admin user error:', error);
    throw error;
  }
}

/**
 * Delete an admin user (placeholder for future implementation)
 */
export async function deleteAdminUser(userId: string): Promise<any> {
  try {
    const accessToken = getAccessToken();
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }
    
    const response = await fetch(`/api/admin/users/${userId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: accessToken,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
    
  } catch (error: any) {
    console.error('Delete admin user error:', error);
    throw error;
  }
}
