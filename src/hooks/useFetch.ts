// hooks/useFetch.ts
import { useEffect, useState, useCallback, useRef } from 'react';
import axios, { AxiosRequestConfig, AxiosResponse, CancelTokenSource } from 'axios';
import { getCognitoTokensFromCookies } from '@/utils/auth';
import { refreshTokenIfNeeded, isAccessTokenExpired } from '@/utils/simpleTokenRefresh';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

interface UseFetchOptions {
  method?: HttpMethod;
  body?: any;
  headers?: Record<string, string>;
  isCredentials?: boolean;
  immediate?: boolean;
  timeout?: number;
  params?: Record<string, any>; // URL parameters
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
}

interface UseFetchState<T> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
  status: number | null;
}

interface UseFetchError {
  message: string;
  status?: number;
  code?: string;
  response?: any;
}

// Create axios instance with default config and token refresh logic
const createAxiosInstance = () => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || '',
    timeout: 10000, // 10 seconds default timeout
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor - check and refresh token before every request
  instance.interceptors.request.use(
    async (config) => {
      try {
        // Get current tokens
        const tokens = getCognitoTokensFromCookies();

        if (tokens.accessToken) {
          // Check if token is expired
          if (isAccessTokenExpired(tokens.accessToken)) {
            console.log('🔄 Token expired, refreshing before API call...');

            // Refresh token before making the request
            const refreshSuccess = await refreshTokenIfNeeded();

            if (!refreshSuccess) {
              console.error('❌ Token refresh failed, redirecting to login');
              // Redirect to login if refresh fails
              window.location.href = '/login';
              return Promise.reject(new Error('Token refresh failed'));
            }

            console.log('✅ Token refreshed successfully before API call');

            // Get the new token after refresh
            const newTokens = getCognitoTokensFromCookies();
            if (newTokens.accessToken) {
              config.headers.Authorization = `Bearer ${newTokens.accessToken}`;
            }
          } else {
            // Token is still valid, use it
            config.headers.Authorization = `Bearer ${tokens.accessToken}`;
          }
        }

        return config;
      } catch (error) {
        console.error('❌ Error in request interceptor:', error);
        return config;
      }
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor - handle 401 errors by refreshing token and retrying
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      // If we get 401 and haven't already tried to refresh
      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        console.log('🔄 Got 401 error, attempting token refresh and retry...');

        try {
          // Attempt to refresh token
          const refreshSuccess = await refreshTokenIfNeeded();

          if (refreshSuccess) {
            console.log('✅ Token refreshed after 401, retrying original request');

            // Get the new token and retry the original request
            const newTokens = getCognitoTokensFromCookies();
            if (newTokens.accessToken) {
              originalRequest.headers.Authorization = `Bearer ${newTokens.accessToken}`;
              return instance(originalRequest);
            }
          } else {
            console.error('❌ Token refresh failed after 401, redirecting to login');
            // Redirect to login if refresh fails
            window.location.href = '/login';
          }
        } catch (refreshError) {
          console.error('❌ Error during token refresh after 401:', refreshError);
          // Redirect to login if refresh fails
          window.location.href = '/login';
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

export function useFetch<T = any>(
  url: string,
  options: UseFetchOptions = {}
) {
  const {
    method = 'GET',
    body,
    headers: customHeaders = {},
    isCredentials = false,
    immediate = true,
    timeout = 10000,
    params,
    responseType = 'json',
  } = options;

  const [state, setState] = useState<UseFetchState<T>>({
    data: null,
    error: null,
    isLoading: false,
    status: null,
  });

  // Use ref to track if component is mounted
  const isMountedRef = useRef(true);
  const cancelTokenRef = useRef<CancelTokenSource | null>(null);
  const axiosInstanceRef = useRef(createAxiosInstance());

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('Component unmounted');
      }
    };
  }, []);

  const fetchData = useCallback(
    async (overrideOptions?: Partial<UseFetchOptions>) => {
      // Cancel previous request if still pending
      console.log(overrideOptions,'overrideOptions',overrideOptions?.method || method)
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('New request initiated');
      }

      // Create new cancel token for this request
      const cancelTokenSource = axios.CancelToken.source();
      cancelTokenRef.current = cancelTokenSource;

      if (!isMountedRef.current) return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));


      try {
        const finalMethod = overrideOptions?.method || method;
        const finalBody = overrideOptions?.body || body;
        const finalHeaders = { ...customHeaders, ...(overrideOptions?.headers || {}) };
        const finalParams = { ...params, ...(overrideOptions?.params || {}) };
        const finalTimeout = overrideOptions?.timeout || timeout;
        const finalResponseType = overrideOptions?.responseType || responseType;

        // Build request config
        const requestConfig: AxiosRequestConfig = {
          method: finalMethod,
          url: url.startsWith('http') ? url : url, // Axios instance handles baseURL
          headers: finalHeaders,
          timeout: finalTimeout,
          cancelToken: cancelTokenSource.token,
          params: finalParams,
          responseType: finalResponseType,
        };

        // Authorization header is now handled automatically by interceptors
        // Just mark that this request needs credentials
        if (isCredentials || overrideOptions?.isCredentials) {
          // The request interceptor will automatically add the Authorization header
          // with a valid (refreshed if needed) token
        }

        // Add body for methods that support it
        if (finalMethod !== 'GET' && finalMethod !== 'HEAD' && finalBody !== undefined) {
          if (finalBody instanceof FormData) {
            requestConfig.data = finalBody;
            // Remove Content-Type for FormData (let browser set it)
            delete requestConfig.headers!['Content-Type'];
          } else {
            requestConfig.data = finalBody;
          }
        }

        const response: AxiosResponse<T> = await axiosInstanceRef.current.request(requestConfig);

        // Check if request was cancelled
        if (axios.isCancel(response)) {
          return;
        }

        if (!isMountedRef.current) return;

        setState({
          data: response.data,
          error: null,
          isLoading: false,
          status: response.status,
        });

        return response.data;
      } catch (err: any) {
        // Don't set error state if request was cancelled
        if (axios.isCancel(err) || !isMountedRef.current) {
          return;
        }

        let errorDetails: UseFetchError = {
          message: 'Unknown error occurred',
        };

        if (axios.isAxiosError(err)) {
          errorDetails = {
            message: err.message,
            status: err.response?.status,
            code: err.code,
            response: err.response?.data,
          };

          // Custom error messages based on status codes
          if (err.response?.status === 401) {
            errorDetails.message = 'Session expired - Please login again';
          } else if (err.response?.status === 403) {
            errorDetails.message = 'Forbidden - You do not have permission';
          } else if (err.response?.status === 404) {
            errorDetails.message = 'Resource not found';
          } else if (err.response?.status && err.response.status >= 500) {
            errorDetails.message = 'Server error - Please try again later';
          } else if (err.code === 'ECONNABORTED') {
            errorDetails.message = 'Request timeout - Please try again';
          } else if (err.response?.data?.message) {
            errorDetails.message = err.response.data.message;
          }
        } else {
          errorDetails.message = err.message || 'Network error occurred';
        }

        if (!isMountedRef.current) return;

        setState({
          data: null,
          error: errorDetails.message,
          isLoading: false,
          status: errorDetails.status || null,
        });

        throw errorDetails;
      }
    },
    [url, method, body, customHeaders, isCredentials, timeout, params, responseType]
  );

  // Effect for immediate fetching
  useEffect(() => {
    if (immediate && method === 'GET') {
      fetchData();
    }
  }, [immediate, method]); // Removed fetchData from deps to avoid infinite loops

  // Cleanup function for manual abort
  const abort = useCallback(() => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.cancel('Request aborted by user');
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // Reset function to clear state
  const reset = useCallback(() => {
    setState({
      data: null,
      error: null,
      isLoading: false,
      status: null,
    });
  }, []);

  return {
    data: state.data,
    error: state.error,
    isLoading: state.isLoading,
    status: state.status,
    refetch: () => fetchData(),
    send: (sendOptions?: Partial<UseFetchOptions>) => fetchData(sendOptions),
    abort,
    reset,
  };
}

// Specialized hooks for common use cases
export function useGet<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'GET' });
}

export function usePost<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'POST', immediate: false });
}

export function usePut<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'PUT', immediate: false });
}

export function useDelete<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'DELETE', immediate: false });
}