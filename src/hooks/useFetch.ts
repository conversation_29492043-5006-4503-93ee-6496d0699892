// hooks/useFetch.ts
import { useEffect, useState, useCallback, useRef } from 'react';
import axios, { AxiosRequestConfig, AxiosResponse, CancelTokenSource } from 'axios';
import { getAuthToken } from '@/utils/auth';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

interface UseFetchOptions {
  method?: HttpMethod;
  body?: any;
  headers?: Record<string, string>;
  isCredentials?: boolean;
  immediate?: boolean;
  timeout?: number;
  params?: Record<string, any>; // URL parameters
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
}

interface UseFetchState<T> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
  status: number | null;
}

interface UseFetchError {
  message: string;
  status?: number;
  code?: string;
  response?: any;
}

// Create axios instance with default config
const createAxiosInstance = () => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || '',
    timeout: 10000, // 10 seconds default timeout
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor for auth tokens
  instance.interceptors.request.use(
    (config) => {
      // You can add global request logic here
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Response interceptor for global error handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // You can add global error handling here
      return Promise.reject(error);
    }
  );

  return instance;
};

export function useFetch<T = any>(
  url: string,
  options: UseFetchOptions = {}
) {
  const {
    method = 'GET',
    body,
    headers: customHeaders = {},
    isCredentials = false,
    immediate = true,
    timeout = 10000,
    params,
    responseType = 'json',
  } = options;

  const [state, setState] = useState<UseFetchState<T>>({
    data: null,
    error: null,
    isLoading: false,
    status: null,
  });

  // Use ref to track if component is mounted
  const isMountedRef = useRef(true);
  const cancelTokenRef = useRef<CancelTokenSource | null>(null);
  const axiosInstanceRef = useRef(createAxiosInstance());

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('Component unmounted');
      }
    };
  }, []);

  const fetchData = useCallback(
    async (overrideOptions?: Partial<UseFetchOptions>) => {
      // Cancel previous request if still pending
      console.log(overrideOptions,'overrideOptions',overrideOptions?.method || method)
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('New request initiated');
      }

      // Create new cancel token for this request
      const cancelTokenSource = axios.CancelToken.source();
      cancelTokenRef.current = cancelTokenSource;

      if (!isMountedRef.current) return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));


      try {
        const finalMethod = overrideOptions?.method || method;
        const finalBody = overrideOptions?.body || body;
        const finalHeaders = { ...customHeaders, ...(overrideOptions?.headers || {}) };
        const finalParams = { ...params, ...(overrideOptions?.params || {}) };
        const finalTimeout = overrideOptions?.timeout || timeout;
        const finalResponseType = overrideOptions?.responseType || responseType;

        // Build request config
        const requestConfig: AxiosRequestConfig = {
          method: finalMethod,
          url: url.startsWith('http') ? url : url, // Axios instance handles baseURL
          headers: finalHeaders,
          timeout: finalTimeout,
          cancelToken: cancelTokenSource.token,
          params: finalParams,
          responseType: finalResponseType,
        };

        // Add authorization header if credentials are required
        if (isCredentials || overrideOptions?.isCredentials) {
          const token = getAuthToken();
          if (token) {
            requestConfig.headers = {
              ...requestConfig.headers,
              Authorization: `Bearer ${token}`,
            };
          }
        }

        // Add body for methods that support it
        if (finalMethod !== 'GET' && finalMethod !== 'HEAD' && finalBody !== undefined) {
          if (finalBody instanceof FormData) {
            requestConfig.data = finalBody;
            // Remove Content-Type for FormData (let browser set it)
            delete requestConfig.headers!['Content-Type'];
          } else {
            requestConfig.data = finalBody;
          }
        }

        const response: AxiosResponse<T> = await axiosInstanceRef.current.request(requestConfig);

        // Check if request was cancelled
        if (axios.isCancel(response)) {
          return;
        }

        if (!isMountedRef.current) return;

        setState({
          data: response.data,
          error: null,
          isLoading: false,
          status: response.status,
        });

        return response.data;
      } catch (err: any) {
        // Don't set error state if request was cancelled
        if (axios.isCancel(err) || !isMountedRef.current) {
          return;
        }

        let errorDetails: UseFetchError = {
          message: 'Unknown error occurred',
        };

        if (axios.isAxiosError(err)) {
          errorDetails = {
            message: err.message,
            status: err.response?.status,
            code: err.code,
            response: err.response?.data,
          };

          // Custom error messages based on status codes
          if (err.response?.status === 401) {
            errorDetails.message = 'Unauthorized - Please check your credentials';
          } else if (err.response?.status === 403) {
            errorDetails.message = 'Forbidden - You do not have permission';
          } else if (err.response?.status === 404) {
            errorDetails.message = 'Resource not found';
          } else if (err.response?.status && err.response.status >= 500) {
            errorDetails.message = 'Server error - Please try again later';
          } else if (err.code === 'ECONNABORTED') {
            errorDetails.message = 'Request timeout - Please try again';
          } else if (err.response?.data?.message) {
            errorDetails.message = err.response.data.message;
          }
        } else {
          errorDetails.message = err.message || 'Network error occurred';
        }

        if (!isMountedRef.current) return;

        setState({
          data: null,
          error: errorDetails.message,
          isLoading: false,
          status: errorDetails.status || null,
        });

        throw errorDetails;
      }
    },
    [url, method, body, customHeaders, isCredentials, timeout, params, responseType]
  );

  // Effect for immediate fetching
  useEffect(() => {
    if (immediate && method === 'GET') {
      fetchData();
    }
  }, [immediate, method]); // Removed fetchData from deps to avoid infinite loops

  // Cleanup function for manual abort
  const abort = useCallback(() => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.cancel('Request aborted by user');
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // Reset function to clear state
  const reset = useCallback(() => {
    setState({
      data: null,
      error: null,
      isLoading: false,
      status: null,
    });
  }, []);

  return {
    data: state.data,
    error: state.error,
    isLoading: state.isLoading,
    status: state.status,
    refetch: () => fetchData(),
    send: (sendOptions?: Partial<UseFetchOptions>) => fetchData(sendOptions),
    abort,
    reset,
  };
}

// Specialized hooks for common use cases
export function useGet<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'GET' });
}

export function usePost<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'POST', immediate: false });
}

export function usePut<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'PUT', immediate: false });
}

export function useDelete<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'DELETE', immediate: false });
}