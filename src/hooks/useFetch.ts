// hooks/useFetch.ts
import { useEffect, useState, useCallback, useRef } from 'react';
import axios, { AxiosRequestConfig, AxiosResponse, CancelTokenSource } from 'axios';
import { getCognitoTokensFromCookies } from '@/utils/auth';
import { refreshTokenIfNeeded, isAccessTokenExpired } from '@/utils/simpleTokenRefresh';

type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

interface UseFetchOptions {
  method?: HttpMethod;
  body?: any;
  headers?: Record<string, string>;
  isCredentials?: boolean;
  immediate?: boolean;
  timeout?: number;
  params?: Record<string, any>; // URL parameters
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
}

interface UseFetchState<T> {
  data: T | null;
  error: string | null;
  isLoading: boolean;
  status: number | null;
}

interface UseFetchError {
  message: string;
  status?: number;
  code?: string;
  response?: any;
}

// Create axios instance with basic config (no global interceptors)
const createAxiosInstance = () => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || '',
    timeout: 10000, // 10 seconds default timeout
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return instance;
};

// Helper function to ensure we have a valid token
const ensureValidToken = async (): Promise<string | null> => {
  try {
    console.log('🔍 Checking token validity...');

    // Get current tokens
    const tokens = getCognitoTokensFromCookies();

    if (!tokens.accessToken) {
      console.log('❌ No access token found');
      return null;
    }

    // Check if token is expired
    if (isAccessTokenExpired(tokens.accessToken)) {
      console.log('🔄 Token expired, refreshing...');

      // Refresh token
      const refreshSuccess = await refreshTokenIfNeeded();

      if (!refreshSuccess) {
        console.error('❌ Token refresh failed');
        return null;
      }

      console.log('✅ Token refreshed successfully');

      // Get the new token after refresh
      const newTokens = getCognitoTokensFromCookies();
      return newTokens.accessToken;
    } else {
      console.log('✅ Token is still valid');
      return tokens.accessToken;
    }
  } catch (error) {
    console.error('❌ Error ensuring valid token:', error);
    return null;
  }
};

// Helper function to make authenticated request with retry logic
const makeAuthenticatedRequest = async (
  instance: any,
  config: AxiosRequestConfig
): Promise<AxiosResponse> => {
  let retryCount = 0;
  const maxRetries = 1;

  while (retryCount <= maxRetries) {
    try {
      // Ensure we have a valid token before making the request
      const validToken = await ensureValidToken();

      if (!validToken) {
        console.error('❌ No valid token available, redirecting to login');
        window.location.href = '/login';
        throw new Error('No valid token available');
      }

      // Add the valid token to the request
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${validToken}`
      };

      console.log('🚀 Making authenticated API request:', {
        url: config.url,
        method: config.method,
        hasAuthHeader: !!config.headers?.Authorization,
        authHeader: typeof config.headers?.Authorization === 'string'
          ? config.headers.Authorization.substring(0, 20) + '...'
          : config.headers?.Authorization
      });

      // Make the request
      const response = await instance.request(config);

      console.log('✅ API request successful:', {
        status: response.status,
        statusText: response.statusText
      });
      return response;

    } catch (error: any) {
      // If we get 401 and haven't retried yet, try to refresh and retry
      if (error.response?.status === 401 && retryCount < maxRetries) {
        console.log('🔄 Got 401 error, attempting token refresh and retry...');
        retryCount++;

        try {
          // Force refresh the token
          const refreshSuccess = await refreshTokenIfNeeded();

          if (refreshSuccess) {
            console.log('✅ Token refreshed after 401, retrying request...');
            // Continue the loop to retry with new token
            continue;
          } else {
            console.error('❌ Token refresh failed after 401');
            window.location.href = '/login';
            throw error;
          }
        } catch (refreshError) {
          console.error('❌ Error during token refresh after 401:', refreshError);
          window.location.href = '/login';
          throw error;
        }
      } else {
        // Not a 401 or already retried, throw the error
        throw error;
      }
    }
  }

  throw new Error('Max retries exceeded');
};

export function useFetch<T = any>(
  url: string,
  options: UseFetchOptions = {}
) {
  const {
    method = 'GET',
    body,
    headers: customHeaders = {},
    isCredentials = false,
    immediate = true,
    timeout = 10000,
    params,
    responseType = 'json',
  } = options;

  const [state, setState] = useState<UseFetchState<T>>({
    data: null,
    error: null,
    isLoading: false,
    status: null,
  });

  // Use ref to track if component is mounted
  const isMountedRef = useRef(true);
  const cancelTokenRef = useRef<CancelTokenSource | null>(null);
  const axiosInstanceRef = useRef(createAxiosInstance());

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('Component unmounted');
      }
    };
  }, []);

  const fetchData = useCallback(
    async (overrideOptions?: Partial<UseFetchOptions>) => {
      // Cancel previous request if still pending
      console.log(overrideOptions,'overrideOptions',overrideOptions?.method || method)
      if (cancelTokenRef.current) {
        cancelTokenRef.current.cancel('New request initiated');
      }

      // Create new cancel token for this request
      const cancelTokenSource = axios.CancelToken.source();
      cancelTokenRef.current = cancelTokenSource;

      if (!isMountedRef.current) return;

      setState(prev => ({ ...prev, isLoading: true, error: null }));


      try {
        const finalMethod = overrideOptions?.method || method;
        const finalBody = overrideOptions?.body || body;
        const finalHeaders = { ...customHeaders, ...(overrideOptions?.headers || {}) };
        const finalParams = { ...params, ...(overrideOptions?.params || {}) };
        const finalTimeout = overrideOptions?.timeout || timeout;
        const finalResponseType = overrideOptions?.responseType || responseType;

        // Build request config
        const requestConfig: AxiosRequestConfig = {
          method: finalMethod,
          url: url.startsWith('http') ? url : url, // Axios instance handles baseURL
          headers: finalHeaders,
          timeout: finalTimeout,
          cancelToken: cancelTokenSource.token,
          params: finalParams,
          responseType: finalResponseType,
        };

        // Handle authentication for requests that need credentials
        const needsAuth = isCredentials || overrideOptions?.isCredentials;

        console.log('🔍 useFetch Debug:', {
          url: requestConfig.url,
          method: finalMethod,
          needsAuth,
          isCredentials,
          overrideIsCredentials: overrideOptions?.isCredentials
        });

        // Add body for methods that support it
        if (finalMethod !== 'GET' && finalMethod !== 'HEAD' && finalBody !== undefined) {
          if (finalBody instanceof FormData) {
            requestConfig.data = finalBody;
            // Remove Content-Type for FormData (let browser set it)
            delete requestConfig.headers!['Content-Type'];
          } else {
            requestConfig.data = finalBody;
          }
        }

        // Make the request - use authenticated request if credentials are needed
        const response: AxiosResponse<T> = needsAuth
          ? await makeAuthenticatedRequest(axiosInstanceRef.current, requestConfig)
          : await axiosInstanceRef.current.request(requestConfig);

        // Check if request was cancelled
        if (axios.isCancel(response)) {
          return;
        }

        if (!isMountedRef.current) return;

        setState({
          data: response.data,
          error: null,
          isLoading: false,
          status: response.status,
        });

        return response.data;
      } catch (err: any) {
        // Don't set error state if request was cancelled
        if (axios.isCancel(err) || !isMountedRef.current) {
          return;
        }

        let errorDetails: UseFetchError = {
          message: 'Unknown error occurred',
        };

        if (axios.isAxiosError(err)) {
          errorDetails = {
            message: err.message,
            status: err.response?.status,
            code: err.code,
            response: err.response?.data,
          };

          // Custom error messages based on status codes
          if (err.response?.status === 401) {
            errorDetails.message = 'Session expired - Please login again';
          } else if (err.response?.status === 403) {
            errorDetails.message = 'Forbidden - You do not have permission';
          } else if (err.response?.status === 404) {
            errorDetails.message = 'Resource not found';
          } else if (err.response?.status && err.response.status >= 500) {
            errorDetails.message = 'Server error - Please try again later';
          } else if (err.code === 'ECONNABORTED') {
            errorDetails.message = 'Request timeout - Please try again';
          } else if (err.response?.data?.message) {
            errorDetails.message = err.response.data.message;
          }
        } else {
          errorDetails.message = err.message || 'Network error occurred';
        }

        if (!isMountedRef.current) return;

        setState({
          data: null,
          error: errorDetails.message,
          isLoading: false,
          status: errorDetails.status || null,
        });

        throw errorDetails;
      }
    },
    [url, method, body, customHeaders, isCredentials, timeout, params, responseType]
  );

  // Effect for immediate fetching
  useEffect(() => {
    if (immediate && method === 'GET') {
      fetchData();
    }
  }, [immediate, method]); // Removed fetchData from deps to avoid infinite loops

  // Cleanup function for manual abort
  const abort = useCallback(() => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.cancel('Request aborted by user');
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  // Reset function to clear state
  const reset = useCallback(() => {
    setState({
      data: null,
      error: null,
      isLoading: false,
      status: null,
    });
  }, []);

  return {
    data: state.data,
    error: state.error,
    isLoading: state.isLoading,
    status: state.status,
    refetch: () => fetchData(),
    send: (sendOptions?: Partial<UseFetchOptions>) => fetchData(sendOptions),
    abort,
    reset,
  };
}

// Specialized hooks for common use cases
export function useGet<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'GET' });
}

export function usePost<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'POST', immediate: false });
}

export function usePut<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'PUT', immediate: false });
}

export function useDelete<T = any>(url: string, options: Omit<UseFetchOptions, 'method'> = {}) {
  return useFetch<T>(url, { ...options, method: 'DELETE', immediate: false });
}