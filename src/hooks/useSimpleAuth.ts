'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { checkAuthAndRefresh, refreshTokenIfNeeded } from '@/utils/simpleTokenRefresh';
import { performCompleteLogout } from '@/utils/auth';
import { signOut } from 'aws-amplify/auth';

interface SimpleAuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useSimpleAuth = () => {
  const router = useRouter();
  const [authState, setAuthState] = useState<SimpleAuthState>({
    isAuthenticated: false,
    isLoading: true,
    error: null
  });

  // Check authentication status and refresh if needed
  const checkAuth = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const isAuth = await checkAuthAndRefresh();
      
      setAuthState({
        isAuthenticated: isAuth,
        isLoading: false,
        error: null
      });

      return isAuth;
    } catch (error: any) {
      console.error('Auth check failed:', error);
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: error.message || 'Authentication check failed'
      });
      return false;
    }
  }, []);

  // Refresh tokens manually
  const refreshTokens = useCallback(async (): Promise<boolean> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const success = await refreshTokenIfNeeded();
      
      setAuthState(prev => ({
        ...prev,
        isAuthenticated: success,
        isLoading: false,
        error: success ? null : 'Token refresh failed'
      }));

      return success;
    } catch (error: any) {
      console.error('Token refresh failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Token refresh failed'
      }));
      return false;
    }
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      // Sign out from Cognito
      await signOut();
      
      // Clear all local data
      performCompleteLogout();
      
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: null
      });

      // Redirect to login
      router.push('/login');
      
    } catch (error: any) {
      console.error('Logout error:', error);
      
      // Even if Cognito signOut fails, clear local data
      performCompleteLogout();
      
      setAuthState({
        isAuthenticated: false,
        isLoading: false,
        error: null
      });
      
      router.push('/login');
    }
  }, [router]);

  // Check auth on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Check auth when user returns to tab
  useEffect(() => {
    const handleFocus = () => {
      checkAuth();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [checkAuth]);

  return {
    ...authState,
    checkAuth,
    refreshTokens,
    logout
  };
};
