'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Checkbox,
  FormControlLabel,
  Divider,
  Alert,
  useTheme,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Security as SecurityIcon,
  Check as CheckIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Role, Permission, SYSTEM_PERMISSIONS, PERMISSION_CATEGORIES } from '@/types/role';

// Validation schema
const roleSchema = z.object({
  name: z
    .string()
    .min(1, 'Role name is required')
    .min(2, 'Role name must be at least 2 characters')
    .max(50, 'Role name must be less than 50 characters'),
  
  description: z
    .string()
    .min(1, 'Description is required')
    .min(10, 'Description must be at least 10 characters')
    .max(200, 'Description must be less than 200 characters'),
  
  level: z
    .number()
    .min(1, 'Level must be at least 1')
    .max(10, 'Level must be at most 10'),
  
  permissions: z
    .array(z.string())
    .min(1, 'At least one permission must be selected'),
});

interface RoleFormProps {
  role?: Role; // If provided, form is in edit mode
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
  currentUserRole?: string;
}

export function RoleForm({
  role,
  onSubmit,
  onCancel,
  loading = false,
  error,
  currentUserRole = 'admin',
}: RoleFormProps) {
  const theme = useTheme();
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [selectAllPermissions, setSelectAllPermissions] = useState(false);

  const isEditMode = !!role;

  // Check if current user can manage the target role
  const canManageRole = (targetRole?: Role) => {
    if (!targetRole) return true; // Creating new role
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1
    };
    const currentLevel = roleHierarchy[currentUserRole as keyof typeof roleHierarchy] || 0;
    return currentLevel > targetRole.level / 3;
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm({
    resolver: zodResolver(roleSchema),
    defaultValues: role ? {
      name: role.name,
      description: role.description,
      level: role.level,
      permissions: role.permissions.map(p => p.id),
    } : {
      name: '',
      description: '',
      level: 5,
      permissions: [],
    },
    mode: 'onChange',
  });

  const watchedPermissions = watch('permissions');

  // Handle select all permissions
  const handleSelectAllPermissions = (checked: boolean) => {
    setSelectAllPermissions(checked);
    if (checked) {
      setValue('permissions', SYSTEM_PERMISSIONS.map(p => p.id));
    } else {
      setValue('permissions', []);
    }
  };

  // Handle category selection
  const handleCategorySelect = (category: string, checked: boolean) => {
    const categoryPermissions = SYSTEM_PERMISSIONS
      .filter(p => p.category === category)
      .map(p => p.id);
    
    const currentPermissions = watchedPermissions || [];
    
    if (checked) {
      // Add category permissions
      const newPermissions = [...new Set([...currentPermissions, ...categoryPermissions])];
      setValue('permissions', newPermissions);
    } else {
      // Remove category permissions
      const newPermissions = currentPermissions.filter(id => !categoryPermissions.includes(id));
      setValue('permissions', newPermissions);
    }
  };

  // Handle individual permission selection
  const handlePermissionSelect = (permissionId: string, checked: boolean) => {
    const currentPermissions = watchedPermissions || [];
    
    if (checked) {
      setValue('permissions', [...currentPermissions, permissionId]);
    } else {
      setValue('permissions', currentPermissions.filter(id => id !== permissionId));
    }
  };

  // Check if all permissions in a category are selected
  const isCategorySelected = (category: string) => {
    const categoryPermissions = SYSTEM_PERMISSIONS.filter(p => p.category === category);
    const selectedPermissions = watchedPermissions || [];
    return categoryPermissions.every(p => selectedPermissions.includes(p.id));
  };

  // Check if some permissions in a category are selected
  const isCategoryPartiallySelected = (category: string) => {
    const categoryPermissions = SYSTEM_PERMISSIONS.filter(p => p.category === category);
    const selectedPermissions = watchedPermissions || [];
    const selectedCount = categoryPermissions.filter(p => selectedPermissions.includes(p.id)).length;
    return selectedCount > 0 && selectedCount < categoryPermissions.length;
  };

  // Get permissions by category
  const getPermissionsByCategory = () => {
    return PERMISSION_CATEGORIES.map(category => ({
      category,
      permissions: SYSTEM_PERMISSIONS.filter(p => p.category === category)
    }));
  };

  const handleFormSubmit = async (data: any) => {
    try {
      await onSubmit(data);
      if (!isEditMode) {
        reset();
      }
    } catch (error) {
      // Error handling is done by parent component
    }
  };

  // Update select all state when permissions change
  useEffect(() => {
    const allPermissions = SYSTEM_PERMISSIONS.map(p => p.id);
    const selectedPermissions = watchedPermissions || [];
    setSelectAllPermissions(selectedPermissions.length === allPermissions.length);
  }, [watchedPermissions]);

  return (
    <Box component="form" onSubmit={handleSubmit(handleFormSubmit)}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Basic Information */}
      <Typography variant="h6" gutterBottom>
        Basic Information
      </Typography>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Role Name"
                placeholder="Enter role name"
                error={!!errors.name}
                helperText={errors.name?.message}
                disabled={loading || (isEditMode && role?.isSystem)}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="level"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.level}>
                <InputLabel>Role Level</InputLabel>
                <Select {...field} label="Role Level" disabled={loading || (isEditMode && role?.isSystem)}>
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((level) => (
                    <MenuItem key={level} value={level}>
                      Level {level} - {level >= 8 ? 'High' : level >= 5 ? 'Medium' : level >= 3 ? 'Low' : 'Basic'}
                    </MenuItem>
                  ))}
                </Select>
                {errors.level && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                    {errors.level.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Description"
                placeholder="Describe the role's purpose and responsibilities"
                multiline
                rows={3}
                error={!!errors.description}
                helperText={errors.description?.message}
                disabled={loading || (isEditMode && role?.isSystem)}
              />
            )}
          />
        </Grid>
      </Grid>

      <Divider sx={{ my: 3 }} />

      {/* Permissions */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SecurityIcon />
          Permissions
        </Typography>
        <FormControlLabel
          control={
            <Checkbox
              checked={selectAllPermissions}
              onChange={(e) => handleSelectAllPermissions(e.target.checked)}
              disabled={loading}
            />
          }
          label="Select All Permissions"
        />
      </Box>

      {errors.permissions && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errors.permissions.message}
        </Alert>
      )}

      {/* Permission Categories */}
      {getPermissionsByCategory().map(({ category, permissions }) => (
        <Accordion
          key={category}
          expanded={expandedCategory === category}
          onChange={() => setExpandedCategory(expandedCategory === category ? null : category)}
          sx={{ mb: 1 }}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isCategorySelected(category)}
                    indeterminate={isCategoryPartiallySelected(category)}
                    onChange={(e) => handleCategorySelect(category, e.target.checked)}
                    disabled={loading}
                    onClick={(e) => e.stopPropagation()}
                  />
                }
                label={category}
              />
              <Chip
                label={`${permissions.filter(p => (watchedPermissions || []).includes(p.id)).length}/${permissions.length}`}
                size="small"
                color="primary"
                variant="outlined"
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={1}>
              {permissions.map((permission) => (
                <Grid item xs={12} sm={6} key={permission.id}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={(watchedPermissions || []).includes(permission.id)}
                        onChange={(e) => handlePermissionSelect(permission.id, e.target.checked)}
                        disabled={loading}
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {permission.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {permission.description}
                        </Typography>
                      </Box>
                    }
                  />
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      ))}

      {/* Selected Permissions Summary */}
      {watchedPermissions && watchedPermissions.length > 0 && (
        <Box sx={{ mt: 3, p: 2, backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Selected Permissions ({watchedPermissions.length})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {watchedPermissions.map((permissionId) => {
              const permission = SYSTEM_PERMISSIONS.find(p => p.id === permissionId);
              return permission ? (
                <Chip
                  key={permissionId}
                  label={permission.name}
                  size="small"
                  color="primary"
                  variant="outlined"
                  onDelete={() => handlePermissionSelect(permissionId, false)}
                  disabled={loading}
                />
              ) : null;
            })}
          </Box>
        </Box>
      )}
    </Box>
  );
} 