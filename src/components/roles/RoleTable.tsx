'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Typography,
  Box,
  Tooltip,
  Checkbox,
  TablePagination,
  TableSortLabel,
  useTheme,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  People as PeopleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { Role } from '@/types/role';

interface RoleTableProps {
  roles: Role[];
  onEdit: (role: Role) => void;
  onDelete: (role: Role) => void;
  onAssignPermissions: (role: Role) => void;
  selectedRoles: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  loading?: boolean;
  currentUserRole?: string;
}

type SortField = 'name' | 'level' | 'memberCount' | 'createdAt';
type SortOrder = 'asc' | 'desc';

export function RoleTable({
  roles,
  onEdit,
  onDelete,
  onAssignPermissions,
  selectedRoles,
  onSelectionChange,
  loading = false,
  currentUserRole = 'admin',
}: RoleTableProps) {
  const theme = useTheme();
  const [sortField, setSortField] = useState<SortField>('level');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Check if current user can manage the target role
  const canManageRole = (targetRole: Role) => {
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1
    };
    const currentLevel = roleHierarchy[currentUserRole as keyof typeof roleHierarchy] || 0;
    return currentLevel > targetRole.level / 3; // Approximate level mapping
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    const isAsc = sortField === field && sortOrder === 'asc';
    setSortOrder(isAsc ? 'desc' : 'asc');
    setSortField(field);
  };

  // Sort roles
  const sortedRoles = [...roles].sort((a, b) => {
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];

    if (sortField === 'createdAt') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    if (aValue < bValue) {
      return sortOrder === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortOrder === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // Pagination
  const paginatedRoles = sortedRoles.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  // Handle selection
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const selectableRoles = paginatedRoles
        .filter(role => !role.isSystem && canManageRole(role))
        .map(role => role.id);
      onSelectionChange(selectableRoles);
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectRole = (roleId: string) => {
    const newSelected = selectedRoles.includes(roleId)
      ? selectedRoles.filter(id => id !== roleId)
      : [...selectedRoles, roleId];
    onSelectionChange(newSelected);
  };

  const getRoleLevelColor = (level: number) => {
    if (level >= 8) return 'error';
    if (level >= 5) return 'warning';
    if (level >= 3) return 'info';
    return 'success';
  };

  const getRoleLevelLabel = (level: number) => {
    if (level >= 8) return 'High';
    if (level >= 5) return 'Medium';
    if (level >= 3) return 'Low';
    return 'Basic';
  };

  return (
    <Paper elevation={2} sx={{ overflow: 'hidden' }}>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: theme.palette.grey[50] }}>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedRoles.length > 0 && selectedRoles.length < paginatedRoles.filter(r => !r.isSystem && canManageRole(r)).length}
                  checked={selectedRoles.length > 0 && selectedRoles.length === paginatedRoles.filter(r => !r.isSystem && canManageRole(r)).length}
                  onChange={handleSelectAll}
                  disabled={loading}
                />
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'name'}
                  direction={sortField === 'name' ? sortOrder : 'asc'}
                  onClick={() => handleSort('name')}
                >
                  Role Name
                </TableSortLabel>
              </TableCell>
              <TableCell>Description</TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'level'}
                  direction={sortField === 'level' ? sortOrder : 'asc'}
                  onClick={() => handleSort('level')}
                >
                  Level
                </TableSortLabel>
              </TableCell>
              <TableCell>Permissions</TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'memberCount'}
                  direction={sortField === 'memberCount' ? sortOrder : 'asc'}
                  onClick={() => handleSort('memberCount')}
                >
                  Members
                </TableSortLabel>
              </TableCell>
              <TableCell>
                <TableSortLabel
                  active={sortField === 'createdAt'}
                  direction={sortField === 'createdAt' ? sortOrder : 'asc'}
                  onClick={() => handleSort('createdAt')}
                >
                  Created
                </TableSortLabel>
              </TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedRoles.map((role) => {
              const isSelected = selectedRoles.includes(role.id);
              const canManage = canManageRole(role);

              return (
                <TableRow
                  key={role.id}
                  hover
                  selected={isSelected}
                  sx={{
                    '&:hover': {
                      backgroundColor: theme.palette.action.hover,
                    },
                  }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={isSelected}
                      onChange={() => handleSelectRole(role.id)}
                      disabled={loading || role.isSystem || !canManage}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {role.name}
                      </Typography>
                      {role.isSystem && (
                        <Chip
                          label="System"
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {role.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={`${getRoleLevelLabel(role.level)} (${role.level})`}
                      size="small"
                      color={getRoleLevelColor(role.level) as any}
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <SecurityIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        {role.permissions.length}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PeopleIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        {role.memberCount || 0}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(role.createdAt).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                      <Tooltip title="Edit Role">
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => onEdit(role)}
                            disabled={loading || !canManage}
                            color="primary"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                      
                      <Tooltip title="Manage Permissions">
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => onAssignPermissions(role)}
                            disabled={loading || !canManage}
                            color="info"
                          >
                            <SecurityIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                      
                      <Tooltip 
                        title={
                          role.isSystem 
                            ? "Cannot delete system roles" 
                            : role.memberCount && role.memberCount > 0
                            ? "Cannot delete role with assigned members"
                            : "Delete Role"
                        }
                      >
                        <span>
                          <IconButton
                            size="small"
                            onClick={() => onDelete(role)}
                            disabled={loading || role.isSystem || (role.memberCount && role.memberCount > 0) || !canManage}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={sortedRoles.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(_, newPage) => setPage(newPage)}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
      />
    </Paper>
  );
} 