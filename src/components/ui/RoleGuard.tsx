'use client';

import React from 'react';

interface RoleGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: 'super_admin' | 'admin' | 'moderator';
  fallback?: React.ReactNode;
}

export function RoleGuard({
  children,
  requiredPermissions = [],
  requiredRole,
  fallback = null,
}: RoleGuardProps) {
  // Mock current user role and permissions - in real app, this would come from auth context
  const currentUserRole = 'super_admin';
  const currentUserPermissions = [
    'members.view', 'members.create', 'members.edit', 'members.delete', 'members.export',
    'admin_users.view', 'admin_users.create', 'admin_users.edit', 'admin_users.delete',
    'roles.view', 'roles.create', 'roles.edit', 'roles.delete', 'roles.assign',
    'analytics.view', 'analytics.export',
    'dashboard.view',
    'settings.view', 'settings.edit',
    'users.view', 'users.edit', 'users.delete',
  ];

  // Check role requirement
  if (requiredRole) {
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1
    };
    
    const currentLevel = roleHierarchy[currentUserRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;
    
    if (currentLevel < requiredLevel) {
      return <>{fallback}</>;
    }
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const hasAllPermissions = requiredPermissions.every(permission =>
      currentUserPermissions.includes(permission)
    );
    
    if (!hasAllPermissions) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
} 