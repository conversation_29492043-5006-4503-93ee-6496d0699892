'use client';

import React, { useState } from 'react';
import { useGet, usePost } from '@/hooks/useFetch';
import { Box, Button, Typography, Alert, Paper } from '@mui/material';
import { getCognitoTokensFromCookies } from '@/utils/auth';
import { isAccessTokenExpired } from '@/utils/simpleTokenRefresh';

export const TokenRefreshTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [manualTestResult, setManualTestResult] = useState<string>('');

  // Test GET request with auto token refresh
  const { data, error, isLoading, refetch } = useGet<any>(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/admin-list`,
    {
      isCredentials: true,
      immediate: false, // Don't fetch immediately
    }
  );

  // Test POST request
  const { send: sendPost, isLoading: isPostLoading, error: postError } = usePost<any>(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/test-endpoint`,
    {
      isCredentials: true,
    }
  );

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const checkCurrentTokenStatus = () => {
    const tokens = getCognitoTokensFromCookies();
    
    if (!tokens.accessToken) {
      addTestResult('❌ No access token found in cookies');
      return;
    }

    const isExpired = isAccessTokenExpired(tokens.accessToken);
    addTestResult(`🔍 Token status: ${isExpired ? 'EXPIRED' : 'VALID'}`);
    
    // Show token expiry time
    try {
      const decoded = JSON.parse(atob(tokens.accessToken.split('.')[1]));
      const expTime = new Date(decoded.exp * 1000);
      addTestResult(`⏰ Token expires at: ${expTime.toLocaleString()}`);
    } catch (e) {
      addTestResult('❌ Could not decode token');
    }
  };

  const testGetRequest = async () => {
    addTestResult('🚀 Testing GET request with auto token refresh...');
    checkCurrentTokenStatus();
    
    try {
      await refetch();
      addTestResult('✅ GET request successful!');
    } catch (err: any) {
      addTestResult(`❌ GET request failed: ${err.message}`);
    }
  };

  const testPostRequest = async () => {
    addTestResult('🚀 Testing POST request with auto token refresh...');
    checkCurrentTokenStatus();
    
    try {
      const result = await sendPost({
        body: { test: 'data' }
      });
      addTestResult('✅ POST request successful!');
    } catch (err: any) {
      addTestResult(`❌ POST request failed: ${err.message}`);
    }
  };

  const testManualApiCall = async () => {
    setManualTestResult('Testing manual API call...');
    
    try {
      // Make a direct fetch call to test the token refresh logic
      const tokens = getCognitoTokensFromCookies();
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/admin-list`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${tokens.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setManualTestResult('✅ Manual API call successful');
      } else {
        setManualTestResult(`❌ Manual API call failed: ${response.status} ${response.statusText}`);
      }
    } catch (error: any) {
      setManualTestResult(`❌ Manual API call error: ${error.message}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setManualTestResult('');
  };

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Token Refresh Test Dashboard
      </Typography>

      <Box mb={3}>
        <Typography variant="h6" gutterBottom>
          Current Status:
        </Typography>
        <Button variant="outlined" onClick={checkCurrentTokenStatus} sx={{ mr: 2 }}>
          Check Token Status
        </Button>
        <Button variant="outlined" onClick={clearResults}>
          Clear Results
        </Button>
      </Box>

      <Box mb={3}>
        <Typography variant="h6" gutterBottom>
          useFetch Hook Tests:
        </Typography>
        <Button 
          variant="contained" 
          onClick={testGetRequest} 
          disabled={isLoading}
          sx={{ mr: 2 }}
        >
          {isLoading ? 'Testing GET...' : 'Test GET Request'}
        </Button>
        <Button 
          variant="contained" 
          onClick={testPostRequest} 
          disabled={isPostLoading}
          sx={{ mr: 2 }}
        >
          {isPostLoading ? 'Testing POST...' : 'Test POST Request'}
        </Button>
        <Button variant="outlined" onClick={testManualApiCall}>
          Test Manual API Call
        </Button>
      </Box>

      {/* Show current request status */}
      {(data || error) && (
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Last GET Request Result:
          </Typography>
          {error && <Alert severity="error">Error: {error}</Alert>}
          {data && <Alert severity="success">Success: Data received</Alert>}
        </Box>
      )}

      {(postError) && (
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Last POST Request Result:
          </Typography>
          <Alert severity="error">Error: {postError}</Alert>
        </Box>
      )}

      {manualTestResult && (
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Manual API Call Result:
          </Typography>
          <Alert severity={manualTestResult.includes('✅') ? 'success' : 'error'}>
            {manualTestResult}
          </Alert>
        </Box>
      )}

      {/* Test Results Log */}
      <Paper elevation={2} sx={{ p: 2, maxHeight: 400, overflow: 'auto' }}>
        <Typography variant="h6" gutterBottom>
          Test Results Log:
        </Typography>
        {testResults.length === 0 ? (
          <Typography color="text.secondary">No test results yet...</Typography>
        ) : (
          testResults.map((result, index) => (
            <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace', mb: 0.5 }}>
              {result}
            </Typography>
          ))
        )}
      </Paper>
    </Box>
  );
};
