'use client';

import { useBackgroundTokenRefresh } from '@/hooks/useBackgroundTokenRefresh';

interface BackgroundTokenRefreshProps {
  checkInterval?: number; // Check every X milliseconds (default: 30 seconds)
  refreshBuffer?: number; // Refresh X minutes before expiry (default: 5 minutes)
}

/**
 * Component that silently refreshes tokens in the background
 * Add this to your app layout to enable automatic token refresh
 */
export const BackgroundTokenRefresh: React.FC<BackgroundTokenRefreshProps> = ({
  checkInterval = 30000, // 30 seconds
  refreshBuffer = 5 // 5 minutes
}) => {
  useBackgroundTokenRefresh({
    checkInterval,
    refreshBuffer
  });

  // This component doesn't render anything - it just runs the background refresh logic
  return null;
};
