import React from 'react';
import { Box, Button, Checkbox, FormControlLabel, TextField } from '@mui/material';
import { MFAFormData } from '@/types/auth';
import { AUTH_CONSTANTS } from '@/constants/auth';

interface MFAFormProps {
  mfaData: MFAFormData;
  setMfaData: (data: MFAFormData) => void;
  onSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
}

export const MFAForm: React.FC<MFAFormProps> = ({
  mfaData,
  setMfaData,
  onSubmit,
  isLoading,
}) => {
  return (
    <Box component="form" onSubmit={onSubmit} sx={{ mt: 2, width: "100%" }}>
      <TextField
        margin="normal"
        required
        fullWidth
        label="Verification Code"
        placeholder="Enter 6-digit code"
        value={mfaData.code}
        onChange={(e) => setMfaData({ ...mfaData, code: e.target.value })}
        sx={{ background: "#fafbfc", borderRadius: 1 }}
        inputProps={{ 
          maxLength: AUTH_CONSTANTS.FORM_VALIDATION.MFA_CODE_LENGTH, 
          pattern: AUTH_CONSTANTS.FORM_VALIDATION.MFA_CODE_PATTERN 
        }}
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={mfaData.rememberDevice}
            onChange={(e) =>
              setMfaData({ ...mfaData, rememberDevice: e.target.checked })
            }
            color="primary"
          />
        }
        label={AUTH_CONSTANTS.MESSAGES.REMEMBER_DEVICE}
      />
      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isLoading}
        sx={{
          mt: 3,
          mb: 2,
          fontWeight: 700,
          background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
          color: "#fff",
          "&:hover": {
            opacity: 0.95,
          },
        }}
      >
        {isLoading ? "Verifying..." : "Verify"}
      </Button>
    </Box>
  );
};
