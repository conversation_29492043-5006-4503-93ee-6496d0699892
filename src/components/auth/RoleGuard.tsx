'use client';

import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { Security as SecurityIcon } from '@mui/icons-material';
import { useRouter } from 'next/navigation';

interface RoleGuardProps {
  children: React.ReactNode;
  requiredRole: 'super_admin' | 'admin' | 'moderator';
  currentUserRole?: string;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

export function RoleGuard({ 
  children, 
  requiredRole, 
  currentUserRole = 'member',
  fallback,
  redirectTo 
}: RoleGuardProps) {
  const router = useRouter();

  // Role hierarchy for authorization
  const roleHierarchy = {
    super_admin: 3,
    admin: 2,
    moderator: 1,
    member: 0
  };

  const hasPermission = () => {
    const userLevel = roleHierarchy[currentUserRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole];
    return userLevel >= requiredLevel;
  };

  if (!hasPermission()) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (redirectTo) {
      // Redirect to specified page
      React.useEffect(() => {
        router.push(redirectTo);
      }, [router, redirectTo]);
      return null;
    }

    // Default access denied message
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <SecurityIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
        <Typography variant="h5" gutterBottom color="error.main">
          Access Denied
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          You don't have permission to access this page. 
          This area requires {requiredRole.replace('_', ' ')} privileges.
        </Typography>
        <Button 
          variant="contained" 
          onClick={() => router.back()}
          sx={{ mr: 2 }}
        >
          Go Back
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => router.push('/dashboard')}
        >
          Go to Dashboard
        </Button>
      </Box>
    );
  }

  return <>{children}</>;
}

// Higher-order component for role-based route protection
export function withRoleGuard<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole: 'super_admin' | 'admin' | 'moderator',
  options?: {
    currentUserRole?: string;
    fallback?: React.ReactNode;
    redirectTo?: string;
  }
) {
  return function RoleGuardedComponent(props: P) {
    return (
      <RoleGuard
        requiredRole={requiredRole}
        currentUserRole={options?.currentUserRole}
        fallback={options?.fallback}
        redirectTo={options?.redirectTo}
      >
        <Component {...props} />
      </RoleGuard>
    );
  };
}

// Hook for checking permissions
export function useRoleGuard() {
  // In a real app, this would get the current user's role from auth context
  const getCurrentUserRole = (): string => {
    // Mock implementation - replace with actual auth context
    return 'admin';
  };

  const hasRole = (requiredRole: 'super_admin' | 'admin' | 'moderator'): boolean => {
    const currentRole = getCurrentUserRole();
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1,
      member: 0
    };
    
    const userLevel = roleHierarchy[currentRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole];
    return userLevel >= requiredLevel;
  };

  const canManageRole = (targetRole: string): boolean => {
    const currentRole = getCurrentUserRole();
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1,
      member: 0
    };
    
    const userLevel = roleHierarchy[currentRole as keyof typeof roleHierarchy] || 0;
    const targetLevel = roleHierarchy[targetRole as keyof typeof roleHierarchy] || 0;
    return userLevel > targetLevel;
  };

  return {
    hasRole,
    canManageRole,
    getCurrentUserRole,
  };
} 