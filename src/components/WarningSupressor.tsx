'use client';

import { useEffect } from 'react';

export default function WarningSupressor() {
  useEffect(() => {
    // Only suppress warnings if environment variable is set
    if (process.env.NEXT_PUBLIC_SUPPRESS_WARNINGS !== 'true') {
      return;
    }
    
    // Aggressive suppression of scroll behavior warnings
    const originalWarn = console.warn;
    const originalLog = console.log;
    const originalError = console.error;
    
    const shouldSuppress = (message: any) => {
      if (typeof message === 'string') {
        return (
          message.includes('Skipping auto-scroll behavior') ||
          message.includes('position: sticky') ||
          message.includes('position: fixed') ||
          message.includes('MuiBox-root') ||
          message.includes('MuiContainer-root') ||
          message.includes('MuiPaper-root')
        );
      }
      return false;
    };
    
    console.warn = (...args: any[]) => {
      if (!shouldSuppress(args[0])) {
        originalWarn.apply(console, args);
      }
    };
    
    console.log = (...args: any[]) => {
      if (!shouldSuppress(args[0])) {
        originalLog.apply(console, args);
      }
    };
    
    console.error = (...args: any[]) => {
      if (!shouldSuppress(args[0])) {
        originalError.apply(console, args);
      }
    };
    
    // Cleanup function
    return () => {
      console.warn = originalWarn;
      console.log = originalLog;
      console.error = originalError;
    };
  }, []);
  
  return null; // This component renders nothing
}
