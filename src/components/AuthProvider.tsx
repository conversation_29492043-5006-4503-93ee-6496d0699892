'use client';

import { AuthState, LoginFormData, Permission, UserRole } from '@/types/auth';
import { createContext, ReactNode, useContext, useCallback, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  loginRequest,
  logoutRequest,
  completeMFARequest,
  refreshUserRequest,
} from '@/store/auth/redux';
// Import Cognito configuration to ensure it's initialized
import '@/config/cognito';

interface AuthContextType extends AuthState {
  login: (credentials: LoginFormData) => Promise<void>;
  logout: () => Promise<void>;
  completeMFA: (code: string, rememberDevice?: boolean) => Promise<void>;
  refreshUser: () => Promise<void>;
  hasPermission: (permission: Permission) => boolean;
  hasRole: (role: UserRole) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const dispatch = useAppDispatch();
  const authState = useAppSelector((state) => state.auth);

  // Initialize auth state on app load
  useEffect(() => {
    dispatch(refreshUserRequest());
  }, [dispatch]);

  const login = useCallback(
    async (credentials: LoginFormData) => {
      dispatch(loginRequest(credentials));
    },
    [dispatch]
  );

  const logout = useCallback(async () => {
    dispatch(logoutRequest());
  }, [dispatch]);

  const completeMFA = useCallback(
    async (code: string, rememberDevice?: boolean) => {
      dispatch(completeMFARequest({ code, rememberDevice }));
    },
    [dispatch]
  );

  const refreshUser = useCallback(async () => {
    dispatch(refreshUserRequest());
  }, [dispatch]);

  const hasPermission = useCallback(
    (permission: Permission): boolean => {
      if (!authState.user) return false;
      return authState.user.permissions.includes(permission);
    },
    [authState.user]
  );

  const hasRole = useCallback(
    (role: UserRole): boolean => {
      if (!authState.user) return false;
      return authState.user.roles.includes(role);
    },
    [authState.user]
  );

  const contextValue = {
    ...authState,
    login,
    logout,
    completeMFA,
    refreshUser,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={contextValue as any}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook for checking permissions
export const usePermission = (permission: Permission): boolean => {
  const { hasPermission } = useAuth();
  return hasPermission(permission);
};

// Hook for checking roles
export const useRole = (role: UserRole): boolean => {
  const { hasRole } = useAuth();
  return hasRole(role);
}; 