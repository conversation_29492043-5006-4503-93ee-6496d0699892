"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  IconButton,
  Chip,
  Avatar,
  Typography,
  Box,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  TablePagination,
  TableSortLabel,
  useTheme,
  Alert,
  CircularProgress,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from "@mui/icons-material";
import { format } from "date-fns";
import { AdminUser, ADMIN_USER_TABLE_COLUMNS } from "@/types/adminUser";

interface AdminUserTableProps {
  adminUsers: AdminUser[];
  loading?: boolean;
  selectedIds: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  onEdit: (adminUser: AdminUser) => void;
  onDelete: (adminUser: AdminUser) => void;
  onView: (adminUser: AdminUser) => void;
  onStatusChange?: (
    adminUser: AdminUser,
    status: "active" | "inactive" | "pending"
  ) => void;
  onRoleChange?: (
    adminUser: AdminUser,
    role: "super_admin" | "admin" | "moderator"
  ) => void;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  onSortChange?: (sortBy: string, sortOrder: "asc" | "desc") => void;
  page: number;
  pageSize: number;
  total: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  currentUserRole?: string;
}

export function AdminUserTable({
  adminUsers,
  loading = false,
  selectedIds,
  onSelectionChange,
  onEdit,
  onDelete,
  onView,
  onStatusChange,
  onRoleChange,
  sortBy,
  sortOrder,
  onSortChange,
  page,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  currentUserRole = "admin",
}: AdminUserTableProps) {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);

  // Check if current user can manage the target user
  const canManageUser = (targetRole: string) => {
    const roleHierarchy = {
      super_admin: 3,
      admin: 2,
      moderator: 1,
    };
    return (
      roleHierarchy[currentUserRole as keyof typeof roleHierarchy] >
      roleHierarchy[targetRole as keyof typeof roleHierarchy]
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelectedIds = adminUsers.map((user) => user.id);
      onSelectionChange(newSelectedIds);
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectUser = (userId: string) => {
    const selectedIndex = selectedIds.indexOf(userId);
    let newSelectedIds: string[] = [];

    if (selectedIndex === -1) {
      newSelectedIds = newSelectedIds.concat(selectedIds, userId);
    } else if (selectedIndex === 0) {
      newSelectedIds = newSelectedIds.concat(selectedIds.slice(1));
    } else if (selectedIndex === selectedIds.length - 1) {
      newSelectedIds = newSelectedIds.concat(selectedIds.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelectedIds = newSelectedIds.concat(
        selectedIds.slice(0, selectedIndex),
        selectedIds.slice(selectedIndex + 1)
      );
    }

    onSelectionChange(newSelectedIds);
  };

  const handleMenuOpen = (
    event: React.MouseEvent<HTMLElement>,
    user: AdminUser
  ) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const handleSort = (columnId: string) => {
    if (!onSortChange) return;

    const isAsc = sortBy === columnId && sortOrder === "asc";
    onSortChange(columnId, isAsc ? "desc" : "asc");
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "error";
      case "admin":
        return "warning";
      case "moderator":
        return "info";
      default:
        return "default";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "success";
      case "inactive":
        return "error";
      case "pending":
        return "warning";
      default:
        return "default";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircleIcon fontSize="small" />;
      case "inactive":
        return <BlockIcon fontSize="small" />;
      case "pending":
        return <WarningIcon fontSize="small" />;
      default:
        return undefined;
    }
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "Invalid date";
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy HH:mm");
    } catch {
      return "Invalid date";
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (adminUsers.length === 0) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        No admin users found. Create your first admin user to get started.
      </Alert>
    );
  }

  return (
    <Paper sx={{ width: "100%", overflow: "hidden" }}>
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  color="primary"
                  indeterminate={
                    selectedIds.length > 0 &&
                    selectedIds.length < adminUsers.length
                  }
                  checked={
                    adminUsers.length > 0 &&
                    selectedIds.length === adminUsers.length
                  }
                  onChange={handleSelectAll}
                />
              </TableCell>
              {ADMIN_USER_TABLE_COLUMNS.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || "left"}
                  style={{ width: column.width }}
                  sortDirection={sortBy === column.id ? sortOrder : false}
                >
                  {column.sortable ? (
                    <TableSortLabel
                      active={sortBy === column.id}
                      direction={sortBy === column.id ? sortOrder : "asc"}
                      onClick={() => handleSort(column.id)}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}
              <TableCell align="center" style={{ width: "100px" }}>
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {adminUsers.map((user) => {
              const isSelected = selectedIds.indexOf(user.id) !== -1;
              const canManage = canManageUser(user.roles[0]);

              return (
                <TableRow
                  hover
                  key={user.id}
                  selected={isSelected}
                  sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      checked={isSelected}
                      onChange={() => handleSelectUser(user.id)}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                      <Avatar
                        sx={{
                          width: 40,
                          height: 40,
                          fontSize: "0.875rem",
                          bgcolor: theme.palette.primary.main,
                        }}
                        src={user.profileImage}
                      >
                        {getInitials(user.firstName, user.lastName)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {user.firstName} {user.lastName}
                        </Typography>
                        {user.department && (
                          <Typography variant="caption" color="text.secondary">
                            {user.department}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{user.email}</Typography>
                    {user.phone && (
                      <Typography variant="caption" color="text.secondary">
                        {user.phone}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={user.roles[0]?.replace("_", " ")}
                      color={getRoleColor(user.roles[0])}
                      size="small"
                      icon={<SecurityIcon />}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {user.department || "—"}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={user.status}
                      color={getStatusColor(user.status)}
                      size="small"
                      icon={getStatusIcon(user.status)}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Typography variant="body2">
                      {formatDate(user.createdAt)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Typography variant="body2">
                      {user.lastLoginAt
                        ? formatDateTime(user.lastLoginAt)
                        : "Never"}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    {canManage ? (
                      <Tooltip title="Actions">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, user)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </Tooltip>
                    ) : (
                      <Tooltip title="Insufficient permissions to manage this user">
                        <span>
                          <IconButton size="small" disabled>
                            <MoreVertIcon />
                          </IconButton>
                        </span>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <MenuItem
          onClick={() => {
            onView(selectedUser!);
            handleMenuClose();
          }}
        >
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => {
            onEdit(selectedUser!);
            handleMenuClose();
          }}
        >
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit User</ListItemText>
        </MenuItem>

        {/* Status Change Options */}
        {onStatusChange && selectedUser && (
          <>
            {selectedUser.status !== "active" && (
              <MenuItem
                onClick={() => {
                  onStatusChange(selectedUser, "active");
                  handleMenuClose();
                }}
              >
                <ListItemIcon>
                  <CheckCircleIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>Activate</ListItemText>
              </MenuItem>
            )}
            {selectedUser.status !== "inactive" && (
              <MenuItem
                onClick={() => {
                  onStatusChange(selectedUser, "inactive");
                  handleMenuClose();
                }}
              >
                <ListItemIcon>
                  <BlockIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText>Deactivate</ListItemText>
              </MenuItem>
            )}
          </>
        )}

        {/* Role Change Options */}
        {onRoleChange &&
          selectedUser &&
          canManageUser(selectedUser.roles[0]) && (
            <>
              {selectedUser.roles[0] !== "moderator" &&
                canManageUser("moderator") && (
                  <MenuItem
                    onClick={() => {
                      onRoleChange(selectedUser, "moderator");
                      handleMenuClose();
                    }}
                  >
                    <ListItemIcon>
                      <SecurityIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Change to Moderator</ListItemText>
                  </MenuItem>
                )}
              {selectedUser.roles[0] !== "admin" && canManageUser("admin") && (
                <MenuItem
                  onClick={() => {
                    onRoleChange(selectedUser, "admin");
                    handleMenuClose();
                  }}
                >
                  <ListItemIcon>
                    <SecurityIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText>Change to Admin</ListItemText>
                </MenuItem>
              )}
              {selectedUser.roles[0] !== "super_admin" &&
                canManageUser("super_admin") && (
                  <MenuItem
                    onClick={() => {
                      onRoleChange(selectedUser, "super_admin");
                      handleMenuClose();
                    }}
                  >
                    <ListItemIcon>
                      <SecurityIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>Change to Super Admin</ListItemText>
                  </MenuItem>
                )}
            </>
          )}

        <MenuItem
          onClick={() => {
            onDelete(selectedUser!);
            handleMenuClose();
          }}
          sx={{ color: "error.main" }}
        >
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete User</ListItemText>
        </MenuItem>
      </Menu>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={total}
        rowsPerPage={pageSize}
        page={page - 1}
        onPageChange={(_, newPage) => onPageChange(newPage + 1)}
        onRowsPerPageChange={(event) =>
          onPageSizeChange(parseInt(event.target.value, 10))
        }
      />
    </Paper>
  );
}
