"use client";

import React, { useState } from "react";
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  Button,
  CircularProgress,
  Chip,
  OutlinedInput,
} from "@mui/material";
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from "@mui/icons-material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { AdminUser } from "@/types/adminUser";
import { createAdminUser, CreateAdminUserRequest } from "@/services/adminAPI";
import { showToast } from "@/utils/toast";

// Constants
const ROLE_HIERARCHY = {
  super_admin: 3,
  admin: 2,
  moderator: 1,
} as const;

const ROLE_COLORS = {
  super_admin: "error",
  admin: "warning",
  moderator: "info",
  default: "default",
} as const;

const STATUS_COLORS = {
  active: "success",
  inactive: "error",
  pending: "warning",
  default: "default",
} as const;

// Validation schemas
const createAdminUserSchema = z.object({
  username: z
    .string()
    .min(1, "Username is required")
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username must be less than 50 characters")
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      "Username can only contain letters, numbers, underscores, and hyphens"
    )
    .refine(
      (val) => !val.includes("  "),
      "Username cannot contain consecutive spaces"
    )
    .refine(
      (val) => val.toLowerCase() !== "admin",
      'Username cannot be "admin"'
    )
    .refine((val) => val.toLowerCase() !== "root", 'Username cannot be "root"')
    .refine(
      (val) => val.toLowerCase() !== "administrator",
      'Username cannot be "administrator"'
    ),

  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(100, "Email must be less than 100 characters")
    .refine((val) => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(val);
    }, "Please enter a valid email format")
    .refine(
      (val) => !val.includes(".."),
      "Email cannot contain consecutive dots"
    )
    .refine(
      (val) => !val.startsWith(".") && !val.endsWith("."),
      "Email cannot start or end with a dot"
    ),

  roles: z
    .array(z.enum(["super_admin", "admin", "moderator"]))
    .min(1, "At least one role is required")
    .max(3, "Maximum 3 roles allowed")
    .refine((roles) => {
      // Prevent conflicting role combinations
      const hasAdminRole = roles.includes("admin");
      const hasSuperAdminRole = roles.includes("super_admin");
      if (hasSuperAdminRole && hasAdminRole) {
        return false;
      }
      return true;
    }, "Cannot assign both admin and super_admin roles to the same user"),

  temporaryPassword: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(100, "Password must be less than 100 characters")
    .regex(
      /^(?=.*[a-z])/,
      "Password must contain at least one lowercase letter"
    )
    .regex(
      /^(?=.*[A-Z])/,
      "Password must contain at least one uppercase letter"
    )
    .regex(/^(?=.*\d)/, "Password must contain at least one number")
    .regex(
      /^(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/,
      "Password must contain at least one special character"
    )
    .refine((val) => !/\s/.test(val), "Password cannot contain spaces")
    .refine((val) => {
      const commonPasswords = [
        "password",
        "12345678",
        "qwerty123",
        "admin123",
        "password123",
      ];
      return !commonPasswords.includes(val.toLowerCase());
    }, "Password is too common, please choose a stronger password"),
});

const updateAdminUserSchema = createAdminUserSchema.extend({
  id: z.string().min(1, "User ID is required").uuid("Invalid user ID format"),
  temporaryPassword: z
    .string()
    .refine((val) => {
      if (!val || val === "") return true; // Password is optional for updates
      return val.length >= 8;
    }, "Password must be at least 8 characters when provided")
    .refine((val) => {
      if (!val || val === "") return true;
      return /^(?=.*[a-z])/.test(val);
    }, "Password must contain at least one lowercase letter when provided")
    .refine((val) => {
      if (!val || val === "") return true;
      return /^(?=.*[A-Z])/.test(val);
    }, "Password must contain at least one uppercase letter when provided")
    .refine((val) => {
      if (!val || val === "") return true;
      return /^(?=.*\d)/.test(val);
    }, "Password must contain at least one number when provided")
    .refine((val) => {
      if (!val || val === "") return true;
      return /^(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(val);
    }, "Password must contain at least one special character when provided"),
});

// Types
type AdminRole = keyof typeof ROLE_HIERARCHY;
type AdminStatus = keyof typeof STATUS_COLORS;

interface AdminUserFormProps {
  adminUser?: AdminUser;
  currentUserRole?: string;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

// Custom hooks
const useAdminUserForm = (adminUser?: AdminUser) => {
  const isEditMode = !!adminUser;
  const schema = isEditMode ? updateAdminUserSchema : createAdminUserSchema;

  const defaultValues = isEditMode
    ? {
        id: adminUser!.id,
        username: adminUser!.username,
        email: adminUser!.email,
        roles: adminUser!.roles,
        temporaryPassword: "",
      }
    : {
        username: "",
        email: "",
        roles: ["moderator"] as const,
        temporaryPassword: "",
      };

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any,
    mode: "onChange",
  });

  return {
    ...form,
    isEditMode,
  };
};

// Utility functions
const canManageRole = (
  currentUserRole: string,
  targetRole: string
): boolean => {
  const currentLevel = ROLE_HIERARCHY[currentUserRole as AdminRole] || 0;
  const targetLevel = ROLE_HIERARCHY[targetRole as AdminRole] || 0;
  return currentLevel > targetLevel;
};

const getRoleColor = (role: string) => {
  return ROLE_COLORS[role as keyof typeof ROLE_COLORS] || ROLE_COLORS.default;
};

const getStatusColor = (status: string) => {
  return (
    STATUS_COLORS[status as keyof typeof STATUS_COLORS] || STATUS_COLORS.default
  );
};

const formatRoleDisplay = (role: string): string => {
  return role.replace("_", " ").toUpperCase();
};

// Validation helper functions
const validateEmailDomain = (email: string): boolean => {
  const allowedDomains = ["company.com", "organization.org"]; // Add your allowed domains
  const domain = email.split("@")[1];
  return !allowedDomains.length || allowedDomains.includes(domain);
};

const validateUsernameAvailability = async (
  username: string
): Promise<boolean> => {
  // This would typically make an API call to check username availability
  // For now, we'll simulate with some reserved usernames
  const reservedUsernames = [
    "admin",
    "root",
    "administrator",
    "system",
    "user",
  ];
  return !reservedUsernames.includes(username.toLowerCase());
};

const getPasswordStrength = (
  password: string
): { score: number; feedback: string[] } => {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) score += 1;
  else feedback.push("Use at least 8 characters");

  if (/[a-z]/.test(password)) score += 1;
  else feedback.push("Include lowercase letters");

  if (/[A-Z]/.test(password)) score += 1;
  else feedback.push("Include uppercase letters");

  if (/\d/.test(password)) score += 1;
  else feedback.push("Include numbers");

  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 1;
  else feedback.push("Include special characters");

  if (password.length >= 12) score += 1;

  return { score, feedback };
};

// Sub-components
const FormSection: React.FC<{
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}> = ({ title, icon, children }) => (
  <>
    <Typography
      variant="h6"
      gutterBottom
      sx={{ display: "flex", alignItems: "center", gap: 1 }}
    >
      {icon}
      {title}
    </Typography>
    {children}
  </>
);

const RoleChipRenderer = ({ selected }: { selected: string[] }) => (
  <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
    {selected.map((role) => (
      <Chip
        key={role}
        label={formatRoleDisplay(role)}
        size="small"
        color={getRoleColor(role) as any}
      />
    ))}
  </Box>
);

const BasicInformationSection: React.FC<{
  control: any;
  errors: any;
  loading: boolean;
  watch: any;
  isEditMode: boolean;
}> = ({ control, errors, loading, watch, isEditMode }) => {
  const [passwordStrength, setPasswordStrength] = useState<{
    score: number;
    feedback: string[];
  }>({ score: 0, feedback: [] });
  const password = watch("temporaryPassword");

  React.useEffect(() => {
    if (password && password.length > 0) {
      setPasswordStrength(getPasswordStrength(password));
    } else {
      setPasswordStrength({ score: 0, feedback: [] });
    }
  }, [password]);

  const getStrengthColor = (score: number) => {
    if (score <= 2) return "error";
    if (score <= 4) return "warning";
    return "success";
  };

  const getStrengthText = (score: number) => {
    if (score <= 2) return "Weak";
    if (score <= 4) return "Medium";
    return "Strong";
  };

  return (
    <FormSection title="Basic Information" icon={<PersonIcon />}>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="username"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Username"
                error={!!errors.username}
                helperText={
                  errors.username?.message ||
                  "Username must be unique and contain only letters, numbers, underscores, and hyphens"
                }
                disabled={loading}
                inputProps={{
                  maxLength: 50,
                  pattern: "[a-zA-Z0-9_-]+",
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="Email"
                type="email"
                error={!!errors.email}
                helperText={
                  errors.email?.message ||
                  "Enter a valid business email address"
                }
                disabled={loading}
                inputProps={{
                  maxLength: 100,
                }}
              />
            )}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Controller
            name="temporaryPassword"
            control={control}
            render={({ field }) => (
              <Box>
                <TextField
                  {...field}
                  fullWidth
                  label={
                    isEditMode
                      ? "New Password (leave blank to keep current)"
                      : "Password"
                  }
                  type="password"
                  error={!!errors.temporaryPassword}
                  helperText={
                    errors.temporaryPassword?.message ||
                    (isEditMode
                      ? "Leave blank to keep current password"
                      : "Must include uppercase, lowercase, number, and special character")
                  }
                  disabled={loading}
                  inputProps={{
                    maxLength: 100,
                  }}
                />
                {password && password.length > 0 && (
                  <Box sx={{ mt: 1 }}>
                    <Typography
                      variant="caption"
                      color={getStrengthColor(passwordStrength.score)}
                    >
                      Password Strength:{" "}
                      {getStrengthText(passwordStrength.score)}
                    </Typography>
                    {passwordStrength.feedback.length > 0 && (
                      <Typography
                        variant="caption"
                        display="block"
                        color="text.secondary"
                      >
                        Suggestions: {passwordStrength.feedback.join(", ")}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            )}
          />
        </Grid>
      </Grid>
    </FormSection>
  );
};

const RoleStatusSection: React.FC<{
  control: any;
  errors: any;
  loading: boolean;
  currentUserRole: string;
  watch: any;
}> = ({ control, errors, loading, currentUserRole, watch }) => {
  const selectedRoles = watch("roles") || [];

  const getRoleValidationMessage = () => {
    if (
      selectedRoles.includes("super_admin") &&
      selectedRoles.includes("admin")
    ) {
      return "Cannot assign both Super Admin and Admin roles to the same user";
    }
    if (selectedRoles.length === 0) {
      return "At least one role must be selected";
    }
    if (selectedRoles.length > 3) {
      return "Maximum 3 roles can be assigned";
    }
    return "";
  };

  const roleValidationMessage = getRoleValidationMessage();

  return (
    <FormSection title="Role Assignment" icon={<SecurityIcon />}>
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6}>
          <Controller
            name="roles"
            control={control}
            render={({ field }) => (
              <FormControl
                fullWidth
                error={!!errors.roles || !!roleValidationMessage}
              >
                <InputLabel>Roles</InputLabel>
                <Select
                  {...field}
                  multiple
                  input={<OutlinedInput label="Roles" />}
                  disabled={loading}
                  renderValue={(selected) => (
                    <RoleChipRenderer selected={selected as string[]} />
                  )}
                >
                  <MenuItem
                    value="moderator"
                    disabled={!canManageRole(currentUserRole, "moderator")}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                      }}
                    >
                      <Typography>Moderator</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Basic content moderation permissions
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem
                    value="admin"
                    disabled={!canManageRole(currentUserRole, "admin")}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                      }}
                    >
                      <Typography>Admin</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Full administrative permissions
                      </Typography>
                    </Box>
                  </MenuItem>
                  <MenuItem
                    value="super_admin"
                    disabled={!canManageRole(currentUserRole, "super_admin")}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                      }}
                    >
                      <Typography>Super Admin</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Complete system control and user management
                      </Typography>
                    </Box>
                  </MenuItem>
                </Select>
                {(errors.roles || roleValidationMessage) && (
                  <Typography
                    variant="caption"
                    color="error"
                    sx={{ mt: 0.5, display: "block" }}
                  >
                    {errors.roles?.message || roleValidationMessage}
                  </Typography>
                )}
                <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mt: 0.5, display: "block" }}
                >
                  Select appropriate roles based on user responsibilities. Roles
                  determine access levels and permissions.
                </Typography>
              </FormControl>
            )}
          />
        </Grid>
      </Grid>
    </FormSection>
  );
};

const FormActions: React.FC<{
  onCancel: () => void;
  loading: boolean;
  isEditMode: boolean;
  isValid: boolean;
  isDirty: boolean;
}> = ({ onCancel, loading, isEditMode, isValid, isDirty }) => (
  <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end", mt: 4 }}>
    <Button
      variant="outlined"
      onClick={onCancel}
      disabled={loading}
      startIcon={<CancelIcon />}
    >
      Cancel
    </Button>
    <Button
      type="submit"
      variant="contained"
      disabled={loading || !isValid || (!isDirty && isEditMode)}
      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
      sx={{
        background: "linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)",
        color: "#fff",
        "&:hover": {
          opacity: 0.95,
        },
        "&:disabled": {
          background: "#ccc",
          color: "#666",
        },
      }}
    >
      {loading ? "Processing..." : isEditMode ? "Update User" : "Create User"}
    </Button>
  </Box>
);

// Main component
export function AdminUserForm({
  adminUser,
  currentUserRole = "admin",
  onSubmit,
  onCancel,
  loading = false,
  error,
}: AdminUserFormProps) {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isDirty },
    reset,
    watch,
    isEditMode,
  } = useAdminUserForm(adminUser);

  const handleCreateUser = async (data: CreateAdminUserRequest) => {
    const loadingToast = showToast.loading("Creating admin user...");

    try {
      const result = await createAdminUser(data);
      showToast.dismiss(loadingToast);

      if (result.success) {
        showToast.success("Admin user created successfully!");
        reset();
        await onSubmit?.(result.data);
      } else {
        showToast.error(result.error || "Failed to create admin user");
      }
    } catch (err: any) {
      showToast.dismiss(loadingToast);
      throw err;
    }
  };
  const handleFormSubmit = async (data: any) => {
    try {
      // Additional form-level validation before submission
      const roles = data.roles || [];

      // Validate role combinations
      if (roles.includes("super_admin") && roles.includes("admin")) {
        showToast.error(
          "Cannot assign both Super Admin and Admin roles to the same user"
        );
        return;
      }

      // Validate current user can assign selected roles
      const canAssignAllRoles = roles.every((role: string) =>
        canManageRole(currentUserRole, role)
      );
      if (!canAssignAllRoles) {
        showToast.error(
          "You do not have permission to assign one or more selected roles"
        );
        return;
      }

      // Validate password strength for new users
      if (!isEditMode && data.temporaryPassword) {
        const passwordStrength = getPasswordStrength(data.temporaryPassword);
        if (passwordStrength.score < 3) {
          showToast.error(
            "Password is too weak. Please choose a stronger password."
          );
          return;
        }
      }

      if (isEditMode) {
        await onSubmit(data);
      } else {
        const createRequest: CreateAdminUserRequest = {
          email: data.email.toLowerCase().trim(), // Normalize email
          username: data.username.trim(), // Trim whitespace
          password: data.temporaryPassword,
          roles: data.roles,
        };
        await handleCreateUser(createRequest);
      }
    } catch (err: any) {
      console.error("Form submission error:", err);
      showToast.error(err.message || "An error occurred");
    }
  };

  return (
    <Box
      component="form"
      id="admin-user-form"
      onSubmit={handleSubmit(handleFormSubmit)}
      sx={{ width: "100%" }}
    >
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <BasicInformationSection
        control={control}
        errors={errors}
        loading={loading}
        watch={watch}
        isEditMode={isEditMode}
      />

      <RoleStatusSection
        control={control}
        errors={errors}
        loading={loading}
        currentUserRole={currentUserRole}
        watch={watch}
      />

      <FormActions
        onCancel={onCancel}
        loading={loading}
        isEditMode={isEditMode}
        isValid={isValid}
        isDirty={isDirty}
      />
    </Box>
  );
}
