'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  Button,
  CircularProgress,
  Chip,
  OutlinedInput,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AdminUser } from '@/types/adminUser';
import { createAdminUser, CreateAdminUserRequest } from '@/services/adminAPI';
import { showToast } from '@/utils/toast';

// Constants
const ROLE_HIERARCHY = {
  super_admin: 3,
  admin: 2,
  moderator: 1
} as const;

const ROLE_COLORS = {
  super_admin: 'error',
  admin: 'warning',
  moderator: 'info',
  default: 'default'
} as const;

const STATUS_COLORS = {
  active: 'success',
  inactive: 'error',
  pending: 'warning',
  default: 'default'
} as const;



// Validation schemas
const createAdminUserSchema = z.object({
  username: z
    .string()
    .min(1, 'Username is required')
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters'),

  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(100, 'Email must be less than 100 characters'),

  roles: z.array(z.enum(['super_admin', 'admin', 'moderator']))
    .min(1, 'At least one role is required')
    .max(3, 'Maximum 3 roles allowed'),

  temporaryPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(100, 'Password must be less than 100 characters'),
});

const updateAdminUserSchema = createAdminUserSchema.extend({
  id: z.string().min(1, 'User ID is required'),
});

// Types
type AdminRole = keyof typeof ROLE_HIERARCHY;
type AdminStatus = keyof typeof STATUS_COLORS;

interface AdminUserFormProps {
  adminUser?: AdminUser;
  currentUserRole?: string;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

// Custom hooks
const useAdminUserForm = (adminUser?: AdminUser) => {
  const isEditMode = !!adminUser;
  const schema = isEditMode ? updateAdminUserSchema : createAdminUserSchema;
  
  const defaultValues = isEditMode
    ? {
        id: adminUser!.id,
        username: adminUser!.username,
        email: adminUser!.email,
        roles: adminUser!.roles,
        temporaryPassword: '',
      }
    : {
        username: '',
        email: '',
        roles: ['moderator'] as const,
        temporaryPassword: '',
      };

  return useForm({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any,
    mode: 'onChange',
  });
};

// Utility functions
const canManageRole = (currentUserRole: string, targetRole: string): boolean => {
  const currentLevel = ROLE_HIERARCHY[currentUserRole as AdminRole] || 0;
  const targetLevel = ROLE_HIERARCHY[targetRole as AdminRole] || 0;
  return currentLevel > targetLevel;
};

const getRoleColor = (role: string) => {
  return ROLE_COLORS[role as keyof typeof ROLE_COLORS] || ROLE_COLORS.default;
};

const getStatusColor = (status: string) => {
  return STATUS_COLORS[status as keyof typeof STATUS_COLORS] || STATUS_COLORS.default;
};

const formatRoleDisplay = (role: string): string => {
  return role.replace('_', ' ').toUpperCase();
};

// Sub-components
const FormSection: React.FC<{ title: string; icon: React.ReactNode; children: React.ReactNode }> = ({
  title,
  icon,
  children,
}) => (
  <>
    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      {icon}
      {title}
    </Typography>
    {children}
  </>
);

const RoleChipRenderer = ({ selected }: { selected: string[] }) => (
  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
    {selected.map((role) => (
      <Chip
        key={role}
        label={formatRoleDisplay(role)}
        size="small"
        color={getRoleColor(role) as any}
      />
    ))}
  </Box>
);

const BasicInformationSection: React.FC<{
  control: any;
  errors: any;
  loading: boolean;
}> = ({ control, errors, loading }) => (
  <FormSection title="Basic Information" icon={<PersonIcon />}>
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6}>
        <Controller
          name="username"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Username"
              error={!!errors.username}
              helperText={errors.username?.message || ''}
              disabled={loading}
            />
          )}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Email"
              type="email"
              error={!!errors.email}
              helperText={errors.email?.message || ''}
              disabled={loading}
            />
          )}
        />
      </Grid>
      <Grid item xs={12} sm={6}>
        <Controller
          name="temporaryPassword"
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              fullWidth
              label="Password"
              type="password"
              error={!!errors.temporaryPassword}
              helperText={errors.temporaryPassword?.message || ''}
              disabled={loading}
            />
          )}
        />
      </Grid>
    </Grid>
  </FormSection>
);

const RoleStatusSection: React.FC<{
  control: any;
  errors: any;
  loading: boolean;
  currentUserRole: string;
}> = ({ control, errors, loading, currentUserRole }) => (
  <FormSection title="Role Assignment" icon={<SecurityIcon />}>
    <Grid container spacing={2} sx={{ mb: 3 }}>
      <Grid item xs={12} sm={6}>
        <Controller
          name="roles"
          control={control}
          render={({ field }) => (
            <FormControl fullWidth error={!!errors.roles}>
              <InputLabel>Roles</InputLabel>
              <Select
                {...field}
                multiple
                input={<OutlinedInput label="Roles" />}
                disabled={loading}
                renderValue={(selected) => <RoleChipRenderer selected={selected as string[]} />}
              >
                <MenuItem value="moderator" disabled={!canManageRole(currentUserRole, 'moderator')}>
                  Moderator
                </MenuItem>
                <MenuItem value="admin" disabled={!canManageRole(currentUserRole, 'admin')}>
                  Admin
                </MenuItem>
                <MenuItem value="super_admin" disabled={!canManageRole(currentUserRole, 'super_admin')}>
                  Super Admin
                </MenuItem>
              </Select>
              {errors.roles && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, display: 'block' }}>
                  {errors.roles.message}
                </Typography>
              )}
            </FormControl>
          )}
        />
      </Grid>
    </Grid>
  </FormSection>
);

const FormActions: React.FC<{
  onCancel: () => void;
  loading: boolean;
  isEditMode: boolean;
}> = ({ onCancel, loading, isEditMode }) => (
  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 4 }}>
    <Button
      variant="outlined"
      onClick={onCancel}
      disabled={loading}
      startIcon={<CancelIcon />}
    >
      Cancel
    </Button>
    <Button
      type="submit"
      variant="contained"
      disabled={loading}
      startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
      sx={{
        background: 'linear-gradient(90deg, #1e3a8a 0%, #f7c873 100%)',
        color: '#fff',
        '&:hover': {
          opacity: 0.95,
        },
      }}
    >
      {loading ? 'Processing...' : isEditMode ? 'Update User' : 'Create User'}
    </Button>
  </Box>
);

// Main component
export function AdminUserForm({
  adminUser,
  currentUserRole = 'admin',
  onSubmit,
  onCancel,
  loading = false,
  error
}: AdminUserFormProps) {
  const isEditMode = !!adminUser;
  const { control, handleSubmit, formState: { errors }, reset } = useAdminUserForm(adminUser);

  const handleCreateUser = async (data: CreateAdminUserRequest) => {
    const loadingToast = showToast.loading('Creating admin user...');
    
    try {
      const result = await createAdminUser(data);
      showToast.dismiss(loadingToast);

      if (result.success) {
        showToast.success('Admin user created successfully!');
        reset();
        await onSubmit?.(result.data);
      } else {
        showToast.error(result.error || 'Failed to create admin user');
      }
    } catch (err: any) {
      showToast.dismiss(loadingToast);
      throw err;
    }
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (isEditMode) {
        await onSubmit(data);
      } else {
        const createRequest: CreateAdminUserRequest = {
          email: data.email,
          username: data.username,
          password: data.temporaryPassword,
          roles: data.roles,
        };
        await handleCreateUser(createRequest);
      }
    } catch (err: any) {
      console.error('Form submission error:', err);
      showToast.error(err.message || 'An error occurred');
    }
  };

  return (
    <Box component="form" id="admin-user-form" onSubmit={handleSubmit(handleFormSubmit)} sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <BasicInformationSection
        control={control}
        errors={errors}
        loading={loading}
      />

      <RoleStatusSection
        control={control}
        errors={errors}
        loading={loading}
        currentUserRole={currentUserRole}
      />

      <FormActions
        onCancel={onCancel}
        loading={loading}
        isEditMode={isEditMode}
      />
    </Box>
  );
}