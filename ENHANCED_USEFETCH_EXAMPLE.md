# Enhanced useFetch Hook with Automatic Token Refresh

## ✅ **What's Enhanced:**

Your `useFetch` hook now automatically:

1. **Checks token before every API call** - If expired, refreshes it first
2. **Handles 401 errors** - Automatically refreshes token and retries the request
3. **Seamless user experience** - User never sees 401 errors or expired token issues
4. **Global solution** - Works for all API calls using the hook

## 🔧 **How It Works:**

### **Request Interceptor:**
```typescript
// Before every API call:
1. Check if access token is expired
2. If expired → Refresh token automatically
3. Add valid token to Authorization header
4. Make API call with fresh token
```

### **Response Interceptor:**
```typescript
// If API returns 401:
1. Refresh token automatically
2. Retry the original request with new token
3. Return successful response to user
```

## 📝 **Usage Examples:**

### **1. GET Request with Auto Token Refresh:**
```tsx
import { useGet } from '@/hooks/useFetch';

function UserProfile() {
  const { data, error, isLoading } = useGet('/api/user/profile', {
    isCredentials: true // This enables automatic token handling
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return <div>Welcome {data?.name}</div>;
}
```

### **2. POST Request with Auto Token Refresh:**
```tsx
import { usePost } from '@/hooks/useFetch';

function CreateUser() {
  const { send, isLoading, error } = usePost('/api/users', {
    isCredentials: true
  });

  const handleSubmit = async (userData) => {
    try {
      const result = await send({
        body: userData
      });
      console.log('User created:', result);
    } catch (err) {
      console.error('Failed to create user:', err);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* form fields */}
    </form>
  );
}
```

### **3. Manual API Call:**
```tsx
import { useFetch } from '@/hooks/useFetch';

function DataManager() {
  const { send } = useFetch('/api/data', {
    method: 'GET',
    isCredentials: true,
    immediate: false
  });

  const fetchData = async () => {
    try {
      const data = await send();
      console.log('Data fetched:', data);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  return <button onClick={fetchData}>Fetch Data</button>;
}
```

## 🎯 **Key Benefits:**

### **Before Enhancement:**
- ❌ API calls fail with 401 when token expires
- ❌ User sees error messages
- ❌ Manual token refresh needed
- ❌ Poor user experience

### **After Enhancement:**
- ✅ **No more 401 errors** - Tokens refreshed automatically
- ✅ **Seamless experience** - User never knows token expired
- ✅ **Global solution** - Works for all API calls
- ✅ **Automatic retry** - Failed requests retried with new token

## 🔄 **Flow Example:**

```
1. User clicks "Save Data" button
2. useFetch checks: "Is token expired?" 
   → YES: Refresh token silently
   → NO: Continue
3. Make API call with valid token
4. If API returns 401:
   → Refresh token automatically
   → Retry same API call
   → Return success to user
5. User sees "Data saved successfully"
   (Never knows token was expired/refreshed)
```

## ⚙️ **Configuration:**

### **Enable Token Handling:**
```tsx
// Set isCredentials: true to enable automatic token refresh
const { data } = useGet('/api/protected-endpoint', {
  isCredentials: true // This enables the token magic!
});
```

### **All Request Types Supported:**
```tsx
// GET with auto token refresh
const { data } = useGet('/api/users', { isCredentials: true });

// POST with auto token refresh  
const { send } = usePost('/api/users', { isCredentials: true });

// PUT with auto token refresh
const { send } = usePut('/api/users/123', { isCredentials: true });

// DELETE with auto token refresh
const { send } = useDelete('/api/users/123', { isCredentials: true });
```

## 🚀 **Result:**

Your Python backend will now **always receive valid tokens**. No more 401 errors, no more expired token issues. The user experience is completely seamless!
