# Simple Cognito Token Validation & Refresh Implementation

This implementation provides a simple solution for:
1. **Checking access token expiry** on every page access
2. **Automatically refreshing tokens** using Cognito refresh token when expired
3. **Proper cookie expiry** that matches Cognito token expiration (1 hour by default)

## Key Changes Made

### 1. Fixed Cookie Expiry Issue
**File: `src/utils/auth.ts`**
- Updated `syncCognitoTokensToCookies()` function
- Now reads actual token expiration from JWT and sets cookie expiry accordingly
- Access/ID tokens: Use actual token expiry (typically 1 hour)
- Refresh token: 30 days expiry

### 2. Simple Token Refresh Utility
**File: `src/utils/simpleTokenRefresh.ts`**
- `isAccessTokenExpired(token)` - Check if access token is expired
- `refreshTokenIfNeeded()` - Refresh tokens if access token is expired
- `checkAuthAndRefresh()` - Check auth status and refresh if needed

### 3. Updated Middleware
**File: `src/middleware.ts`**
- Checks Cognito tokens from cookies on every request
- If token is expired but refresh token exists, redirects to `/auth/refresh`
- If no valid tokens, redirects to login

### 4. Token Refresh Page
**File: `src/app/auth/refresh/page.tsx`**
- Handles automatic token refresh
- Shows loading state during refresh
- Redirects back to original page after successful refresh
- Redirects to login if refresh fails

### 5. Simple Auth Hook
**File: `src/hooks/useSimpleAuth.ts`**
- `checkAuth()` - Check authentication and refresh if needed
- `refreshTokens()` - Manually refresh tokens
- `logout()` - Complete logout with cleanup

## How It Works

### Page Access Flow:
1. User accesses any page
2. Middleware checks `cognito_access_token` cookie
3. If token is valid → Allow access
4. If token is expired but `cognito_refresh_token` exists → Redirect to `/auth/refresh`
5. If no valid tokens → Redirect to login

### Token Refresh Flow:
1. `/auth/refresh` page automatically calls `refreshTokenIfNeeded()`
2. Uses AWS Amplify's `fetchAuthSession({ forceRefresh: true })`
3. Syncs new tokens to cookies with proper expiry
4. Redirects back to original page

## Usage Examples

### 1. Protect a Page Component
```tsx
import { SimpleAuthGuard } from '@/components/auth/SimpleAuthGuard';

export default function DashboardPage() {
  return (
    <SimpleAuthGuard>
      <div>Protected dashboard content</div>
    </SimpleAuthGuard>
  );
}
```

### 2. Use Auth Hook in Component
```tsx
import { useSimpleAuth } from '@/hooks/useSimpleAuth';

export default function MyComponent() {
  const { isAuthenticated, isLoading, checkAuth, logout } = useSimpleAuth();

  if (isLoading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Not authenticated</div>;

  return (
    <div>
      <p>User is authenticated!</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### 3. Manual Token Refresh
```tsx
import { refreshTokenIfNeeded } from '@/utils/simpleTokenRefresh';

// In any component or API call
const handleApiCall = async () => {
  // Ensure tokens are valid before API call
  const isAuth = await refreshTokenIfNeeded();
  
  if (!isAuth) {
    // Redirect to login
    window.location.href = '/login';
    return;
  }

  // Make API call with valid tokens
  const response = await fetch('/api/protected-endpoint');
};
```

## Configuration

### Protected Routes
Edit `src/middleware.ts` to add/remove protected routes:
```typescript
const PUBLIC_ROUTES = ['/', '/register', '/admin', '/forgot-password', '/login', '/adminRegister', '/totp-setup'];
```

### Token Expiry
The system automatically uses the actual token expiration from Cognito (typically 1 hour). No manual configuration needed.

## Benefits

1. **Simple**: Only checks token expiry and refreshes when needed
2. **Automatic**: No manual intervention required
3. **Secure**: Proper token expiry handling
4. **Efficient**: Only refreshes when actually needed
5. **User-friendly**: Seamless experience with automatic refresh

## Files Modified/Created

- ✅ `src/utils/auth.ts` - Fixed cookie expiry
- ✅ `src/utils/simpleTokenRefresh.ts` - Token refresh utilities
- ✅ `src/middleware.ts` - Updated middleware
- ✅ `src/app/auth/refresh/page.tsx` - Refresh page
- ✅ `src/hooks/useSimpleAuth.ts` - Simple auth hook
- ✅ `src/components/auth/SimpleAuthGuard.tsx` - Auth guard component

This implementation focuses on your exact requirements: check token expiry on page access and automatically refresh using Cognito refresh tokens.
