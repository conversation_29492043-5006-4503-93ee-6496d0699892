# useFetch Hook Debug & Fix Guide

## 🐛 **Issues Found & Fixed:**

### **1. Major Issue: Global Interceptors Problem**
**Problem:** The original implementation used global axios interceptors that added Authorization headers to ALL requests, regardless of the `isCredentials` flag.

**Fix:** Removed global interceptors and created a targeted `makeAuthenticatedRequest` function that only handles authentication for requests with `isCredentials: true`.

### **2. Issue: Token Refresh Logic Not Triggered**
**Problem:** The interceptors didn't know about the `isCredentials` flag, so they couldn't properly handle authentication.

**Fix:** Created a dedicated authentication flow that:
- Only runs when `isCredentials: true`
- Checks token validity before every request
- Refreshes tokens if expired
- Retries on 401 errors

### **3. Issue: Poor Error Handling**
**Problem:** 401 errors were reaching the user even when tokens could be refreshed.

**Fix:** Implemented proper retry logic with automatic token refresh on 401 errors.

## 🔧 **New Implementation Flow:**

### **When `isCredentials: true`:**

```
1. User calls API with useFetch hook
2. ensureValidToken() checks if current token is expired
3. If expired → refreshTokenIfNeeded() gets new token
4. makeAuthenticatedRequest() adds valid token to Authorization header
5. Makes API request with valid token
6. If 401 error → Refresh token and retry once
7. Return success or final error to user
```

### **When `isCredentials: false`:**
```
1. Makes normal API request without authentication
2. No token checking or refresh logic
```

## 🧪 **Testing Your Implementation:**

### **1. Test Page Created:**
Visit: `http://localhost:3000/test-token-refresh`

This page provides:
- Token status checker
- GET request test with auto-refresh
- POST request test with auto-refresh
- Manual API call test
- Detailed logging of all operations

### **2. Console Debugging:**
The enhanced hook now logs detailed information:

```
🔍 useFetch Debug: { url, method, needsAuth, isCredentials }
🔍 Checking token validity...
✅ Token is still valid / 🔄 Token expired, refreshing...
🚀 Making authenticated API request: { url, method, hasAuthHeader }
✅ API request successful: { status, statusText }
```

### **3. Test Your adminListAPI.ts:**

```typescript
// This should now work without 401 errors:
const { data, error, isLoading, status, refetch } = useGet<AdminListResponse>(
  `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/admin/admin-list`,
  {
    isCredentials: true, // ✅ Now properly handled
    immediate: true,
  }
);
```

## 🔍 **Step-by-Step Debugging:**

### **1. Check Token Status:**
```typescript
import { getCognitoTokensFromCookies } from '@/utils/auth';
import { isAccessTokenExpired } from '@/utils/simpleTokenRefresh';

const tokens = getCognitoTokensFromCookies();
console.log('Has token:', !!tokens.accessToken);
console.log('Is expired:', isAccessTokenExpired(tokens.accessToken));
```

### **2. Test Token Refresh:**
```typescript
import { refreshTokenIfNeeded } from '@/utils/simpleTokenRefresh';

const success = await refreshTokenIfNeeded();
console.log('Refresh success:', success);
```

### **3. Monitor Network Tab:**
- Check if Authorization header is present
- Verify token format: `Bearer eyJ...`
- Look for 401 responses and subsequent retry requests

## ✅ **Expected Behavior:**

### **Scenario 1: Valid Token**
```
1. API call with isCredentials: true
2. Console: "✅ Token is still valid"
3. Console: "🚀 Making authenticated API request"
4. Console: "✅ API request successful"
5. No 401 errors
```

### **Scenario 2: Expired Token**
```
1. API call with isCredentials: true
2. Console: "🔄 Token expired, refreshing..."
3. Console: "✅ Token refreshed successfully"
4. Console: "🚀 Making authenticated API request"
5. Console: "✅ API request successful"
6. No 401 errors reach the user
```

### **Scenario 3: 401 During Request**
```
1. API call with isCredentials: true
2. Console: "🚀 Making authenticated API request"
3. Server returns 401
4. Console: "🔄 Got 401 error, attempting token refresh and retry..."
5. Console: "✅ Token refreshed after 401, retrying request..."
6. Console: "✅ API request successful"
7. User gets successful response
```

## 🚨 **If Still Getting 401 Errors:**

### **Check These:**

1. **Environment Variables:**
   ```bash
   NEXT_PUBLIC_API_BASE_URL=your_python_backend_url
   NEXT_PUBLIC_AWS_CLIENT_ID=your_cognito_client_id
   ```

2. **Token Format:**
   - Should be JWT format: `eyJ...`
   - Should not be expired
   - Should be in cookies: `cognito_access_token`

3. **Backend Validation:**
   - Ensure your Python backend accepts `Bearer` tokens
   - Check if backend validates Cognito tokens correctly

4. **Network Requests:**
   - Verify Authorization header is present
   - Check token is not malformed

## 🔧 **Quick Fix Verification:**

Run this test in your component:

```typescript
import { useGet } from '@/hooks/useFetch';

// This should work without 401 errors:
const { data, error } = useGet('/api/test-endpoint', {
  isCredentials: true
});

console.log('Error:', error); // Should be null if token refresh works
console.log('Data:', data);   // Should contain your API response
```

The enhanced `useFetch` hook should now handle all token refresh scenarios automatically, ensuring your Python backend always receives valid Cognito tokens.
